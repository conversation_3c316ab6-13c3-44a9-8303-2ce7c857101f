import os
import time
import pandas as pd
import re
import random
import logging
import json
import threading
import queue
import requests
import tempfile
import platform
import sys
from datetime import datetime
import urllib.parse
import traceback

# 尝试导入统一UI主题
try:
    from ui_theme import AmazonUITheme
except ImportError:
    # 如果找不到主题模块，定义简化版的AmazonUITheme
    class AmazonUITheme:
        # 颜色方案
        COLORS = {
            "primary": "#356cac",     # 深蓝色 - 主色
            "secondary": "#4a90e2",   # 亮蓝色 - 次要色
            "accent": "#f89406",      # 橙色 - 强调色
            "background": "#f5f5f5",  # 浅灰色 - 背景色
            "text": "#333333",        # 深灰色 - 文本色
            "light_text": "#666666",  # 中灰色 - 次要文本
            "white": "#ffffff",       # 白色
            "light_gray": "#f0f0f0"   # 更浅的灰色
        }
        
        @classmethod
        def setup_window(cls, root, title, size="800x600", resizable=(True, True), icon=True):
            root.title(title)
            root.geometry(size)
            root.configure(bg=cls.COLORS["background"])
            
            # 设置图标
            if icon:
                icon_path = cls.get_image_path()
                if icon_path and os.path.exists(icon_path):
                    try:
                        root.iconbitmap(icon_path)
                    except:
                        pass
            return root
            
        @classmethod
        def get_image_path(cls):
            if getattr(sys, 'frozen', False):
                base_dir = os.path.dirname(sys.executable)
            else:
                base_dir = os.path.dirname(os.path.abspath(__file__))
            icon_path = os.path.join(base_dir, "icon.ico")
            if os.path.exists(icon_path):
                return icon_path
            return None
        
        @classmethod
        def setup_styles(cls):
            """设置通用ttk样式"""
            style = ttk.Style()
            
            # 设置全局主题
            try:
                style.theme_use("clam")  # 使用clam主题作为基础
            except:
                pass  # 如果主题不可用，使用默认主题
            
            # 背景配置
            style.configure("TFrame", background=cls.COLORS["background"])
            style.configure("TLabelframe", background=cls.COLORS["background"])
            style.configure("TLabelframe.Label", background=cls.COLORS["background"], 
                            foreground=cls.COLORS["primary"], font=("微软雅黑", 11, "bold"))
            
            # 按钮风格
            style.configure("TButton", 
                            background=cls.COLORS["primary"],
                            foreground=cls.COLORS["white"],
                            font=("微软雅黑", 10),
                            padding=8)
            
            style.map("TButton",
                    background=[('active', cls.COLORS["secondary"]), 
                                ('disabled', cls.COLORS["light_gray"])],
                    foreground=[('disabled', cls.COLORS["light_text"])])
            
            # 标签风格
            style.configure("TLabel", 
                            background=cls.COLORS["background"],
                            foreground=cls.COLORS["text"],
                            font=("微软雅黑", 10))
            
            # Entry风格
            style.configure("TEntry", 
                            background=cls.COLORS["white"],
                            foreground=cls.COLORS["text"],
                            fieldbackground=cls.COLORS["white"],
                            padding=3)
            
            # Combobox风格
            style.configure("TCombobox", 
                            background=cls.COLORS["white"],
                            foreground=cls.COLORS["text"],
                            fieldbackground=cls.COLORS["white"],
                            padding=3)
            
            return style

# 检查并安装必要的库
def check_and_install_dependencies():
    """检查并安装必要的依赖库"""
    required_packages = {
        'selenium': 'selenium',
        'amazoncaptcha': 'amazoncaptcha',
        'beautifulsoup4': 'bs4',
        'pandas': 'pandas',
        'requests': 'requests',
        'webdriver_manager': 'webdriver_manager'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"缺少必要的库: {', '.join(missing_packages)}")
        try:
            import subprocess
            for package in missing_packages:
                print(f"正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print("所有依赖库安装完成，重新导入...")
            # 重新导入所有模块
            for package_name, import_name in required_packages.items():
                if package_name in missing_packages:
                    __import__(import_name)
        except Exception as e:
            print(f"安装依赖库时出错: {str(e)}")
            print("请手动安装以下库:")
            for package in missing_packages:
                print(f"    pip install {package}")
            sys.exit(1)

# 检查并安装依赖
try:
    check_and_install_dependencies()
except Exception as e:
    print(f"依赖检查失败: {str(e)}")

# 导入其他必要的库
try:
    from selenium import webdriver
    from selenium.webdriver.edge.service import Service
    from selenium.webdriver.edge.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    import amazoncaptcha
    from bs4 import BeautifulSoup
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog
except ImportError as e:
    print(f"导入库失败: {str(e)}")
    print("请确保所有必要的库都已正确安装")
    sys.exit(1)

# 设置日志
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
logging.basicConfig(
    level=logging.INFO,
    format=LOG_FORMAT,
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LogLevel:
    SIMPLE = 0    # 简易日志（只显示关键信息）
    NORMAL = 1    # 正常日志（默认级别）
    DETAILED = 2  # 详细日志（包含所有细节）

class AmazonAsinScraper:
    def __init__(self, config=None):
        """初始化Amazon ASIN处理器"""
        # 初始化配置
        self.config = config or {}
        
        # 设置日志级别
        self.log_level = self.config.get('log_level', LogLevel.NORMAL)  # 默认为NORMAL级别
        
        # 初始化已处理的ASIN集合，用于防止重复处理
        self.processed_asins = set()
        
        # 设置国家相关属性
        self.countries = {
            "美国": {"domain": "com", "zipcode": "98032", "zipcode_parts": None},
            "日本": {"domain": "co.jp", "zipcode": "060-8588", "zipcode_parts": ["060", "8588"]},
            "加拿大": {"domain": "ca", "zipcode": "B3H 0A9", "zipcode_parts": ["B3H", "0A9"]}
        }
        self.country = "美国"  # 默认国家
        self.country_domain = "com"  # 默认域名
        self.country_zipcode = "98032"  # 默认邮编
        
        # 初始化结果列表和品牌类型分类
        self.results = []
        self.timeout_asins = []
        self.visit_the_brand_asins = []
        self.other_brand_asins = []
        
        # 品牌分类
        self.compliant_brands = []      # 符合条件的品牌
        self.noncompliant_brands = []   # 不符合条件的品牌
        self.brand_compliant_count = 0  # 符合条件的品牌数量
        self.brand_noncompliant_count = 0  # 不符合条件的品牌数量
        
        # 创建一个webdriver空对象，稍后再初始化
        self.driver = None
        
        # 计数器和标志
        self.asin_counter = 0
        self.processed_count = 0
        self.skip_summary = False
        self.headless_mode = False
        
        # 断点续传和实时保存相关
        self.created_result_files = {}
        self.non_unique_brand_asins = []
        
        # 品牌结果分类
        self.brand_results = {
            "visit_the": {
                "compliant": {},
                "non_compliant": []
            },
            "brand": {
                "compliant": {},
                "non_compliant": []
            },
            "no_brand": {
                "compliant": {},
                "non_compliant": []
            }
        }
        
        # 初始化品牌结果分类字典
        for brand_type in self.brand_results.keys():
            for i in range(7):  # 0-6 七种分类
                self.brand_results[brand_type]["compliant"][i] = []
        
        # 系统信息检测
        self.system_info = self.check_system_compatibility()
        
        # 多线程相关设置
        self.asin_queue = queue.Queue()  # 初始化ASIN队列
        self.lock = threading.Lock()  # 线程同步锁
        
        # 结果文件夹将在setup_results_folder中初始化
        
    def check_system_compatibility(self):
        """检查系统兼容性并返回系统信息"""
        system_info = {
            "os": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "architecture": platform.architecture(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": sys.version,
            "is_64bit": sys.maxsize > 2**32,
            "is_win10": False,
            "is_win7": False
        }
        
        # 判断是否为Win10系统
        if system_info["os"] == "Windows" and "10" in system_info["version"]:
            system_info["is_win10"] = True
            self.log_message("检测到Windows 10系统，将使用自动更新驱动模式", always_show=True)
        
        # 判断是否为Win7系统
        if system_info["os"] == "Windows" and "7" in system_info["release"]:
            system_info["is_win7"] = True
            self.log_message("检测到Windows 7系统，将应用特殊兼容性配置", always_show=True)
            
        # 如果是32位系统，发出警告
        if not system_info["is_64bit"]:
            self.log_message("警告: 检测到32位系统，某些功能可能不正常工作。将采用兼容模式。", always_show=True)
            
        # 记录系统信息
        self.log_message(f"系统信息: {system_info}", always_show=True)
            
        return system_info

    def setup_results_folder(self, country_name):
        """设置结果保存的文件夹结构"""
        self.log_message("正在设置结果文件夹结构...", always_show=True)
        
        # 创建主结果文件夹
        results_folder = os.path.join(os.getcwd(), "筛选结果")
        if not os.path.exists(results_folder):
            os.makedirs(results_folder)
            self.log_message(f"创建主结果文件夹: {results_folder}", always_show=True)
        
        # 创建国家文件夹
        country_folder = os.path.join(results_folder, country_name)
        if not os.path.exists(country_folder):
            os.makedirs(country_folder)
            self.log_message(f"创建国家文件夹: {country_folder}", always_show=True)
        
        # 创建时间戳文件夹，确保每次运行都有独立的结果目录
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_folder = os.path.join(country_folder, current_time)
        if not os.path.exists(run_folder):
            os.makedirs(run_folder)
            self.log_message(f"创建运行时间文件夹: {run_folder}", always_show=True)
        
        # 创建"符合"、"不符合"、"品牌为空"和"网络超时"文件夹
        compliant_folder = os.path.join(run_folder, "符合")
        non_compliant_folder = os.path.join(run_folder, "不符合")
        empty_brand_folder = os.path.join(run_folder, "品牌为空")
        timeout_folder = os.path.join(run_folder, "网络超时")
        
        for folder in [compliant_folder, non_compliant_folder, empty_brand_folder, timeout_folder]:
            if not os.path.exists(folder):
                os.makedirs(folder)
                self.log_message(f"创建文件夹: {folder}", always_show=True)
        
        # 定义库存状态和品牌类型
        stock_statuses = ["缺货", "有货"]
        brand_types = ["Visit_the类型", "Brand类型", "无品牌类型"]
        
        # 创建符合条件的文件夹结构
        self.condition_folders = {
            "符合": compliant_folder,
            "不符合": non_compliant_folder,
            "品牌为空": empty_brand_folder,
            "网络超时": timeout_folder
        }
        
        # 在符合条件文件夹下创建库存状态子文件夹
        for stock_status in stock_statuses:
            stock_folder_compliant = os.path.join(compliant_folder, stock_status)
            if not os.path.exists(stock_folder_compliant):
                os.makedirs(stock_folder_compliant)
                self.log_message(f"创建文件夹: {stock_folder_compliant}", always_show=True)
            
            # 在库存状态文件夹下创建品牌类型文件夹
            for brand_type in brand_types:
                brand_folder = os.path.join(stock_folder_compliant, brand_type)
                if not os.path.exists(brand_folder):
                    os.makedirs(brand_folder)
                    self.log_message(f"创建品牌类型文件夹: {brand_folder}", always_show=True)
        
        # 在品牌为空文件夹下创建库存状态子文件夹
        for stock_status in stock_statuses:
            stock_folder_empty = os.path.join(empty_brand_folder, stock_status)
            if not os.path.exists(stock_folder_empty):
                os.makedirs(stock_folder_empty)
                self.log_message(f"创建文件夹: {stock_folder_empty}", always_show=True)
            
            # 在品牌为空的库存状态文件夹下创建品牌类型文件夹
            for brand_type in brand_types:
                brand_folder = os.path.join(stock_folder_empty, brand_type)
                if not os.path.exists(brand_folder):
                    os.makedirs(brand_folder)
                    self.log_message(f"创建品牌类型文件夹: {brand_folder}", always_show=True)
                    
        # 在网络超时文件夹下创建库存状态子文件夹
        for stock_status in stock_statuses:
            stock_folder_timeout = os.path.join(timeout_folder, stock_status)
            if not os.path.exists(stock_folder_timeout):
                os.makedirs(stock_folder_timeout)
                self.log_message(f"创建文件夹: {stock_folder_timeout}", always_show=True)
            
            # 在网络超时的库存状态文件夹下创建品牌类型文件夹
            for brand_type in brand_types:
                brand_folder = os.path.join(stock_folder_timeout, brand_type)
                if not os.path.exists(brand_folder):
                    os.makedirs(brand_folder)
                    self.log_message(f"创建品牌类型文件夹: {brand_folder}", always_show=True)
        
        # 保存路径信息
        self.results_folder = run_folder
        
        # 设置断点文件和超时文件路径
        # 断点文件保存在国家文件夹中，而不是时间戳文件夹，便于续传
        self.checkpoint_file = os.path.join(country_folder, "checkpoint.json")
        # 超时文件仍保存在当前运行文件夹中
        self.timeout_file = os.path.join(self.results_folder, "timeout_asins.xlsx")
        
        self.log_message(f"结果保存路径: {self.results_folder}", always_show=True)
        self.log_message(f"断点文件保存路径: {self.checkpoint_file}", always_show=True)
        self.log_message("文件夹结构设置完成", always_show=True)
        
        return self.results_folder

    def set_log_level(self, level):
        """设置日志级别"""
        if level in [LogLevel.SIMPLE, LogLevel.NORMAL, LogLevel.DETAILED]:
            self.log_level = level
            level_names = {
                LogLevel.SIMPLE: "简易",
                LogLevel.NORMAL: "正常",
                LogLevel.DETAILED: "详细"
            }
            self.log_message(f"日志级别已设置为: {level_names[level]}", always_show=True)
        else:
            self.log_message(f"无效的日志级别: {level}，使用默认级别", always_show=True)

    def log_message(self, message, always_show=False, level=LogLevel.NORMAL):
        """记录日志消息，根据设置的日志级别控制输出"""
        # 始终显示的日志，不考虑级别（仅用于关键错误和程序状态）
        if always_show:
            print(message)
            logger.info(message)
            return
            
        # 根据日志级别控制输出
        if level <= self.log_level:
            # 简易日志：仅记录到文件，不打印到控制台
            if level == LogLevel.SIMPLE:
                logger.info(message)
                # 简易模式下，仅打印重要状态信息
                if "处理品牌" in message or "处理ASIN" in message or "已保存" in message or "完成" in message:
                    print(message)
            # 正常日志：记录到文件并打印到控制台
            elif level == LogLevel.NORMAL:
                logger.info(message)
                print(message)
            # 详细日志：记录详细信息到文件和控制台
            elif level == LogLevel.DETAILED:
                logger.debug(message)
                print(message)
    
    def get_random_user_agent(self):
        """返回一个随机的用户代理，支持从固定列表选择或动态生成"""
        # 添加一个概率选择是使用固定列表还是动态生成
        use_dynamic = random.random() > 0.5  # 50%概率使用动态生成
        
        if use_dynamic:
            # 动态生成用户代理，参考队列.py的实现
            v1 = random.randint(100, 134)  # Chrome/Edge主版本
            v2 = random.randint(10, 25)    # 品牌版本
            v3 = random.randint(400, 600)  # WebKit版本
            
            # 随机选择浏览器类型
            browser_type = random.choice(['Chrome', 'Edge', 'Firefox', 'Safari'])
            os_type = random.choice(['Windows', 'Macintosh'])
            
            if browser_type == 'Chrome':
                return f"Mozilla/5.0 ({os_type} NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36"
            elif browser_type == 'Edge':
                return f"Mozilla/5.0 ({os_type} NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.{random.randint(1000, 9999)}.{random.randint(10, 99)}"
            elif browser_type == 'Firefox':
                return f"Mozilla/5.0 ({os_type} NT 10.0; Win64; x64; rv:{v1}.0) Gecko/20100101 Firefox/{v1}.0"
            elif browser_type == 'Safari' and os_type == 'Macintosh':
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X 12_{random.randint(0, 6)}_{random.randint(0, 9)}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.{random.randint(0, 6)} Safari/605.1.15"
        
        # 从固定列表中选择
        user_agents = [
            # Windows 10 + Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36",
            
            # Windows 10 + Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 Edg/98.0.1108.62",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36 Edg/99.0.1150.39",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36 Edg/100.0.1185.36",
            
            # Windows 10 + Firefox
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/20100101 Firefox/96.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/20100101 Firefox/97.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:98.0) Gecko/20100101 Firefox/98.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/20100101 Firefox/100.0",
            
            # macOS + Chrome
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_0_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_2_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_3_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_4_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36",
            
            # macOS + Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_0_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_1_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_2_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_3_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_4_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15"
        ]
        return random.choice(user_agents)
    
    def setup_selenium_browser(self):
        """设置Microsoft Edge浏览器，支持Win10和Win7系统"""
        try:
            # 获取系统类型
            is_win7 = self.system_info.get("is_win7", False)
            is_win10 = self.system_info.get("is_win10", False)
            is_64bit = self.system_info.get("is_64bit", True)
            
            edge_options = Options()
            
            # 基本配置
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-browser-side-navigation")
            edge_options.add_argument("--disable-notifications")
            
            # 禁用图片加载，提高性能
            edge_options.add_argument("--blink-settings=imagesEnabled=false")
            edge_options.add_experimental_option("prefs", {"profile.managed_default_content_settings.images": 2})
            self.log_message("已禁用图片加载，提高性能", always_show=True)

            # 添加更多反爬虫检测参数
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            edge_options.add_experimental_option("useAutomationExtension", False)
            
            # 设置更真实的用户代理，随机选择一个
            user_agent = self.get_random_user_agent()
            edge_options.add_argument(f"--user-agent={user_agent}")
            self.log_message(f"设置用户代理: {user_agent}", level=LogLevel.DETAILED)
            
            # 无头模式开关，可通过参数控制是否启用
            if hasattr(self, 'headless_mode') and self.headless_mode:
                edge_options.add_argument("--headless=new")  # 使用新版无头模式
                # 无头模式下的额外设置
                edge_options.add_argument("--window-size=1920,1080")
                edge_options.add_argument("--start-maximized")
                edge_options.add_argument("--disable-blink-features=AutomationControlled")
                # 设置WebGL和Canvas指纹信息，减少被检测风险
                edge_options.add_argument("--disable-webgl")
                edge_options.add_argument("--disable-canvas-aa")
                edge_options.add_argument("--disable-2d-canvas-clip-aa")
                edge_options.add_argument("--disable-gl-drawing-for-tests")
                self.log_message("已启用无头模式，浏览器将在后台运行", always_show=True)
            
            # 更全面的Edge浏览器路径检测
            possible_edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                # Edge Beta版本路径
                r"C:\Program Files (x86)\Microsoft\Edge Beta\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge Beta\Application\msedge.exe"
            ]
            
            edge_path = None
            for path in possible_edge_paths:
                if os.path.exists(path):
                    edge_path = path
                    self.log_message(f"找到Edge浏览器: {path}", always_show=True)
                    break
            
            # 如果找到Edge浏览器，设置二进制路径
            if edge_path:
                edge_options.binary_location = edge_path
            else:
                self.log_message("未找到Edge浏览器，尝试使用系统默认设置", always_show=True)
            
            # 加载欧鹭扩展插件
            current_dir = os.path.abspath(os.path.dirname(__file__))
            extension_path = os.path.join(current_dir, "oalur-extension-V1.9.3_3")
            
            # 检查扩展是否存在
            if os.path.exists(extension_path):
                edge_options.add_argument(f"--load-extension={extension_path}")
                self.log_message(f"加载欧陆扩展: {extension_path}", always_show=True)
            else:
                self.log_message(f"未找到欧陆扩展: {extension_path}，请确保扩展目录存在", always_show=True)
                # 尝试查找扩展目录
                self.log_message("正在查找可能的扩展目录...", always_show=True)
                dirs = os.listdir(current_dir)
                for dir_name in dirs:
                    full_path = os.path.join(current_dir, dir_name)
                    if os.path.isdir(full_path) and ("oalur" in dir_name.lower() or "extension" in dir_name.lower()):
                        self.log_message(f"找到可能的扩展目录: {full_path}", always_show=True)
                        edge_options.add_argument(f"--load-extension={full_path}")
                        self.log_message(f"尝试加载扩展: {full_path}", always_show=True)
                        break
            
            # 根据系统类型使用不同的驱动策略
            if is_win7:
                # Win7系统使用固定路径的驱动
                driver_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe"
                if os.path.exists(driver_path):
                    self.log_message(f"Win7系统使用固定路径驱动: {driver_path}", always_show=True)
                    service = Service(driver_path)
                    self.driver = webdriver.Edge(service=service, options=edge_options)
                    self.driver.maximize_window()
                    
                    # 执行CDP命令绕过检测
                    self.execute_cdp_commands()
                    
                    self.log_message("使用固定路径驱动启动Edge浏览器成功", always_show=True)
                    
                    # 验证扩展是否成功加载（为Win7系统也添加这一调用）
                    self.check_extension_loaded()
                    return True
                else:
                    self.log_message(f"固定路径驱动不存在: {driver_path}", always_show=True)
                    raise Exception("Win7系统必须安装Edge浏览器到默认位置")
            else:
                # Win10系统使用自动更新驱动
                self.log_message("Win10系统，使用自动更新并使用最新驱动", always_show=True)
                try:
                    from selenium.webdriver.edge.service import Service as EdgeService
                    from webdriver_manager.microsoft import EdgeChromiumDriverManager
                    
                    # 使用webdriver_manager自动管理驱动
                    service = EdgeService(EdgeChromiumDriverManager().install())
                    self.driver = webdriver.Edge(service=service, options=edge_options)
                    self.driver.maximize_window()
                    
                    # 执行CDP命令绕过检测
                    self.execute_cdp_commands()
                    
                    self.log_message("使用自动更新驱动启动Edge浏览器成功", always_show=True)
                    
                    # 验证扩展是否成功加载
                    self.check_extension_loaded()
                    return True
                except Exception as e:
                    self.log_message(f"自动更新驱动失败: {str(e)}", always_show=True)
                    raise Exception("Win10系统下自动更新驱动失败")
                
        except Exception as e:
            self.log_message(f"启动Edge浏览器时出错: {str(e)}", always_show=True)
            return False
    
    def check_extension_loaded(self):
        """验证欧陆扩展是否成功加载"""
        try:
            # 等待页面加载完成
            time.sleep(2)
            
            # 检查页面标签，查找欧陆安装页面标签
            found_oalur_page = False
            current_url = self.driver.current_url
            self.log_message(f"当前页面URL: {current_url}", always_show=True)
            
            # 获取所有窗口句柄
            all_handles = self.driver.window_handles
            self.log_message(f"检测到 {len(all_handles)} 个浏览器窗口", always_show=True)
            
            # 检查是否存在欧陆安装页面
            for handle in all_handles:
                # 切换到该窗口
                self.driver.switch_to.window(handle)
                curr_url = self.driver.current_url
                
                # 如果是欧陆安装页面
                if "www.oalur.com/landingpage/install" in curr_url:
                    self.log_message(f"找到欧陆扩展安装页面: {curr_url}", always_show=True)
                    found_oalur_page = True
                    # 关闭该标签页
                    self.driver.close()
                    self.log_message("已关闭欧陆扩展安装页面", always_show=True)
                    break
            
            # 确保切换回主窗口
            if len(self.driver.window_handles) > 0:
                self.driver.switch_to.window(self.driver.window_handles[0])
                self.log_message(f"切换回主窗口: {self.driver.current_url}", always_show=True)
            
            # 判断欧陆扩展是否成功加载
            if found_oalur_page:
                self.log_message("欧陆扩展已成功加载", always_show=True)
            else:
                self.log_message("可能未成功加载欧陆扩展，但将继续运行", always_show=True)
                
            # 返回空白页
            self.driver.get("about:blank")
            time.sleep(1)
        except Exception as e:
            self.log_message(f"检查扩展时出错: {str(e)}", always_show=True)
    
    def close_selenium_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.log_message("浏览器已关闭", always_show=True)
            except Exception as e:
                self.log_message(f"关闭浏览器时出错: {str(e)}", always_show=True)
    
    def is_captcha_present(self):
        """检查是否存在验证码"""
        try:
            captcha_elements = [
                "//input[@id='captchacharacters']",  # 验证码输入框
                "//button[contains(text(), 'Continue shopping')]",  # 继续购物按钮
                "//form[contains(@action, '/errors/validateCaptcha')]",  # 验证码表单
                "//div[contains(text(), 'Enter the characters you see below')]",  # 验证码提示文本
                "//div[contains(text(), 'Type the characters you see in this image')]",  # 美国版验证码提示
                "//input[@name='cvf_captcha_input']",  # 另一种验证码输入框
                "//span[contains(text(), 'Enter the characters you see')]",  # 另一种验证码提示
                "//span[contains(text(), 'Type the characters you see')]"  # 另一种美国版验证码提示
            ]

            for element in captcha_elements:
                if self.driver.find_elements(By.XPATH, element):
                    self.log_message("检测到验证码页面", always_show=True)
                    return True
                    
            # 检查URL是否包含验证码相关关键词
            current_url = self.driver.current_url
            captcha_url_indicators = ['captcha', 'validateCaptcha', 'verify', 'puzzle']
            for indicator in captcha_url_indicators:
                if indicator in current_url:
                    self.log_message(f"从URL检测到验证码页面: {current_url}", always_show=True)
                    return True

            return False
        except Exception as e:
            self.log_message(f"检查验证码时出错: {str(e)}")
            return False
    
    def handle_captcha(self):
        """处理验证码"""
        try:
            # 使用amazoncaptcha库解决验证码
            self.log_message("检测到验证码，使用amazoncaptcha解决...", always_show=True)
            
            # 获取验证码图片链接
            captcha_img = None
            img_xpath_patterns = [
                "//form//div/img",  # 标准验证码图片
                "//img[contains(@src, 'captcha')]",  # 包含captcha的img标签
                "//img[contains(@src, 'Captcha')]"   # 包含Captcha的img标签（大写）
            ]
            
            for xpath in img_xpath_patterns:
                try:
                    captcha_img = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    if captcha_img:
                        self.log_message(f"使用 {xpath} 找到验证码图片", always_show=True)
                        break
                except:
                    continue
            
            if not captcha_img:
                self.log_message("未找到验证码图片，无法继续处理", always_show=True)
                return False
                
            img_url = captcha_img.get_attribute("src")
            
            # 下载验证码图片到临时文件
            self.log_message(f"下载验证码图片: {img_url}", always_show=True)
            temp_dir = tempfile.gettempdir()
            temp_captcha_path = os.path.join(temp_dir, f"amazon_captcha_{int(time.time())}.jpg")
            
            # 最多尝试3次下载
            max_download_attempts = 3
            download_success = False
            
            for attempt in range(max_download_attempts):
                try:
                    # 使用requests下载图片
                    response = requests.get(img_url, stream=True, timeout=10)
                    if response.status_code == 200:
                        with open(temp_captcha_path, 'wb') as f:
                            f.write(response.content)
                        download_success = True
                        break
                    else:
                        self.log_message(f"下载验证码图片失败，状态码: {response.status_code}，尝试 {attempt+1}/{max_download_attempts}", always_show=True)
                        time.sleep(2)  # 等待2秒后重试
                except Exception as e:
                    self.log_message(f"下载验证码图片异常: {str(e)}，尝试 {attempt+1}/{max_download_attempts}", always_show=True)
                    time.sleep(2)  # 等待2秒后重试
            
            if not download_success:
                self.log_message("验证码图片下载多次失败，无法继续处理验证码", always_show=True)
                return False
                
            try:
                # 使用amazoncaptcha解析本地验证码图片
                captcha = amazoncaptcha.AmazonCaptcha(temp_captcha_path)
                captcha_code = captcha.solve()
                
                self.log_message(f"验证码识别结果: {captcha_code}", always_show=True)
                
                # 删除临时文件
                try:
                    os.remove(temp_captcha_path)
                except:
                    pass
                
                # 如果验证码为空或非法，返回失败
                if not captcha_code or len(captcha_code) < 4:
                    self.log_message(f"验证码识别结果无效: {captcha_code}", always_show=True)
                    return False
                
                # 查找验证码输入框
                input_found = False
                input_selectors = [
                    (By.ID, "captchacharacters"),
                    (By.NAME, "cvf_captcha_input"),
                    (By.NAME, "captchacharacters"),
                    (By.CSS_SELECTOR, "input[type='text'][name*='captcha']")
                ]
                
                captcha_input = None
                for by, selector in input_selectors:
                    try:
                        captcha_input = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((by, selector))
                        )
                        input_found = True
                        self.log_message(f"使用选择器 {selector} 找到验证码输入框", always_show=True)
                        break
                    except:
                        continue
                
                if not input_found or not captcha_input:
                    self.log_message("未找到验证码输入框，无法继续处理", always_show=True)
                    return False
                
                # 输入验证码
                captcha_input.clear()
                captcha_input.send_keys(captcha_code)
                self.log_message("已输入验证码", always_show=True)
                
                # 查找提交按钮
                button_found = False
                button_selectors = [
                    (By.XPATH, "//button[contains(text(), 'Continue shopping')]"),
                    (By.XPATH, "//input[@type='submit']"),
                    (By.XPATH, "//button[@type='submit']"),
                    (By.XPATH, "//button[contains(text(), 'Submit')]"),
                    (By.XPATH, "//button[contains(@class, 'submit')]"),
                    (By.XPATH, "//input[@aria-labelledby='captchaSubmit']"),
                    (By.ID, "captchaSubmit")
                ]
                
                for by, selector in button_selectors:
                    try:
                        submit_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((by, selector))
                        )
                        submit_button.click()
                        button_found = True
                        self.log_message(f"使用选择器 {selector} 找到并点击提交按钮", always_show=True)
                        break
                    except:
                        continue
                
                if not button_found:
                    self.log_message("未找到提交按钮，尝试使用回车键提交", always_show=True)
                    try:
                        from selenium.webdriver.common.keys import Keys
                        captcha_input.send_keys(Keys.RETURN)
                    except Exception as e:
                        self.log_message(f"使用回车键提交失败: {str(e)}", always_show=True)
                        return False
                
                # 等待页面加载
                time.sleep(3)
                
                # 验证是否仍在验证码页面
                if self.is_captcha_present():
                    self.log_message("验证码未能正确解决，重试...", always_show=True)
                    return False
                else:
                    self.log_message("验证码已成功解决", always_show=True)
                    return True
            except Exception as e:
                self.log_message(f"处理验证码时出现错误: {str(e)}", always_show=True)
                # 确保删除临时文件
                try:
                    if os.path.exists(temp_captcha_path):
                        os.remove(temp_captcha_path)
                except:
                    pass
                return False
                
        except Exception as e:
            self.log_message(f"处理验证码时出现错误: {str(e)}", always_show=True)
            return False

    def load_asins_from_excel(self, filename="amazon_results_美国.xlsx"):
        """从Excel文件加载ASIN列表"""
        try:
            df = pd.read_excel(filename)
            asins = []
            
            # 检查列名 - 不区分大小写
            columns = [col.lower() for col in df.columns]
            
            if 'asin' in columns:
                # 获取原始列名（保留大小写）
                original_col = df.columns[columns.index('asin')]
                asins = df[original_col].dropna().tolist()
                self.log_message(f"成功从Excel的'{original_col}'列加载了{len(asins)}个ASIN", always_show=True)
            elif 'amazon links' in columns:
                # 获取原始列名（保留大小写）
                original_col = df.columns[columns.index('amazon links')]
                # 从URL中提取ASIN
                links = df[original_col].dropna().tolist()
                for link in links:
                    # 从URL中提取ASIN，格式可能是各种国家的亚马逊链接
                    # 支持美国(amazon.com)、日本(amazon.co.jp)、加拿大(amazon.ca)
                    asin_match = re.search(r'/dp/([A-Z0-9]{10})', link)
                    if asin_match:
                        asins.append(asin_match.group(1))
                    else:
                        # 尝试其他可能的URL格式
                        alt_match = re.search(r'/gp/product/([A-Z0-9]{10})', link)
                        if alt_match:
                            asins.append(alt_match.group(1))
                
                # 注释掉从Excel文件覆盖用户国家设置的代码
                """
                # 如果有"国家"列，记录每个ASIN对应的国家
                if '国家' in df.columns and not df['国家'].empty:
                    # 如果Excel中有国家列，则使用它来设置当前国家
                    country = df['国家'].iloc[0]
                    if country in self.countries:
                        self.country = country
                        self.country_domain = self.countries[country]["domain"]
                        self.country_zipcode = self.countries[country]["zipcode"]
                        self.log_message(f"根据Excel文件设置国家为: {country}, 域名: {self.country_domain}, 邮编: {self.country_zipcode}", always_show=True)
                """
                    
                self.log_message(f"成功从Excel的'{original_col}'列提取了{len(asins)}个ASIN", always_show=True)
            else:
                # 查找任何包含"asin"的列（不区分大小写）
                asin_cols = [col for col in df.columns if 'asin' in col.lower()]
                if asin_cols:
                    # 使用第一个匹配的列
                    asins = df[asin_cols[0]].dropna().tolist()
                    self.log_message(f"成功从Excel的'{asin_cols[0]}'列加载了{len(asins)}个ASIN", always_show=True)
                else:
                    self.log_message(f"Excel文件中未找到包含'asin'或'Amazon Links'的列", always_show=True)
                    return []
            
            # 去除重复的ASIN并转换为大写
            asins = [asin.upper() for asin in asins if isinstance(asin, str)]
            asins = list(dict.fromkeys(asins))  # 保持顺序的去重方法
            
            self.log_message(f"最终加载了{len(asins)}个唯一的ASIN", always_show=True)
            return asins
        except Exception as e:
            self.log_message(f"从Excel文件加载ASIN时出错: {str(e)}", always_show=True)
            return []
    
    def search_brand_on_amazon(self, brand):
        """在Amazon上搜索品牌并确定其类别"""
        if not brand:
            self.log_message("品牌名称为空，无法搜索", always_show=True)
            return None
            
        # 去除特殊字符，只保留字母、数字和空格，以提高搜索准确性
        clean_brand = re.sub(r'[^\w\s]', '', brand).strip()
        if not clean_brand:
            self.log_message(f"清理后的品牌名为空: {brand} -> {clean_brand}，无法搜索", always_show=True)
            return None
            
        self.log_message(f"开始在亚马逊{self.country}站点搜索品牌: {brand}", always_show=True)
        
        max_retries = 3
        retry_interval = 10  # 重试间隔10秒
        
        for retry in range(max_retries):
            try:
                # 构建搜索URL
                search_url = f"https://www.amazon.{self.country_domain}/s?k={brand}&language=en_US"
                self.driver.get(search_url)
                
                # 处理验证码
                if self.is_captcha_present():
                    if not self.handle_captcha():
                        self.log_message("无法处理验证码，搜索失败", always_show=True)
                        return {"status": "error", "brand": brand, "error": "无法处理验证码"}
                
                # 等待搜索结果页面基本元素加载
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.s-search-results"))
                    )
                except TimeoutException:
                    self.log_message("搜索页面加载超时", always_show=True)
                    return {"status": "error", "brand": brand, "error": "页面加载超时"}
                
                # 确保页面完全加载完成 - 等待一小段时间让JavaScript完全执行
                time.sleep(2)
                
                # 尝试等待oa-asin-wrap元素出现（如果有的话）
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.oa-asin-wrap"))
                    )
                    self.log_message(f"找到oa-asin-wrap元素，品牌'{brand}'有其他卖家", level=LogLevel.DETAILED)
                except TimeoutException:
                    # 如果超时，说明确实没有这个元素
                    self.log_message(f"确认网页已完全加载，未找到oa-asin-wrap元素，品牌'{brand}'没有其他卖家", always_show=True)
                    return {"status": "success", "brand": brand, "brands_found": [], "sellers": []}
                
                # 使用BeautifulSoup解析页面
                page_source = self.driver.page_source
                soup = BeautifulSoup(page_source, 'html.parser')
                
                # 获取所有 div[class="oa-asin-wrap"] 元素
                elements = soup.find_all('div', class_='oa-asin-wrap')
                
                # 再次确认是否找到元素（防止在等待后页面状态变化）
                if not elements:
                    self.log_message(f"解析后未找到oa-asin-wrap元素，品牌'{brand}'没有其他卖家", always_show=True)
                    return {"status": "success", "brand": brand, "brands_found": [], "sellers": []}
                
                # 初始化品牌数列和卖家数列
                brands = []
                sellers = []
                asins = []
                login_required_brand_count = 0  # 用于计算需要登录查看的品牌数量
                login_required_seller_count = 0  # 用于计算需要登录查看的卖家数量
                empty_brand_count = 0  # 用于计算品牌信息为空的数量
                
                # 遍历元素获取品牌和卖家文本内容
                for element in elements:
                    text = element.get_text()
                    
                    # 获取ASIN
                    asin_match = re.search(r'ASIN[：:]\s*(.*?)[\n]', text)
                    asin = asin_match.group(1).strip() if asin_match else 'no'
                    asins.append(asin)
                    
                    # 获取品牌文本内容
                    brand_match = re.search(r'品牌[：:]\s*[\n]?(.*?)[\n]', text)
                    brand_text = brand_match.group(1).lower().replace(' ', '') if brand_match else 'no'
                    brands.append(brand_text)
                    
                    # 检查品牌信息是否为空（包含hidden属性的div元素）
                    asin_brand_element = element.select_one('.asin_brand')
                    if asin_brand_element:
                        hidden_div = asin_brand_element.select_one('div[hidden]')
                        if hidden_div:
                            self.log_message(f"ASIN {asin}: 品牌信息为空", level=LogLevel.DETAILED)
                            empty_brand_count += 1
                    
                    # 检查品牌是否需要登录查看
                    if "****" in brand_text and "登录后可见" in element.get_text():
                        login_required_brand_count += 1
                    
                    # 获取卖家文本内容
                    seller_match = re.search(r'卖家[：:]\s*(.*?)[\n]', text)
                    seller = seller_match.group(1).lower().replace(' ', '') if seller_match else 'no'
                    sellers.append(seller)
                    
                    # 检查卖家是否需要登录查看
                    if "****" in seller and "登录后可见" in element.get_text():
                        login_required_seller_count += 1
                
                # 计算总的需要登录查看的数量
                total_login_required = login_required_brand_count + login_required_seller_count
                
                # 检查需要登录查看的元素数量
                if total_login_required >= 5:
                    self.log_message(f"发现需要登录后查看的品牌数量:{login_required_brand_count}，卖家数量:{login_required_seller_count}，总计:{total_login_required}，尝试等待10秒后重新获取", always_show=True)
                    
                    # 计数当前页面重载次数
                    page_reload_count = 0
                    max_page_reloads = 3
                    
                    while total_login_required >= 5 and page_reload_count < max_page_reloads:
                        # 等待后重试
                        time.sleep(retry_interval)
                        
                        # 解析页面并重新检查
                        page_source = self.driver.page_source
                        soup = BeautifulSoup(page_source, 'html.parser')
                        elements = soup.find_all('div', class_='oa-asin-wrap')
                        
                        # 重置计数
                        login_required_brand_count = 0
                        login_required_seller_count = 0
                        empty_brand_count = 0  # 重置空品牌计数
                        brands = []
                        sellers = []
                        asins = []
                        
                        # 重新遍历元素
                        for element in elements:
                            text = element.get_text()
                            
                            # 获取ASIN
                            asin_match = re.search(r'ASIN[：:]\s*(.*?)[\n]', text)
                            asin = asin_match.group(1).strip() if asin_match else 'no'
                            asins.append(asin)
                            
                            # 获取品牌文本内容
                            brand_match = re.search(r'品牌[：:]\s*[\n]?(.*?)[\n]', text)
                            brand_text = brand_match.group(1).lower().replace(' ', '') if brand_match else 'no'
                            brands.append(brand_text)
                            
                            # 检查品牌信息是否为空（包含hidden属性的div元素）
                            asin_brand_element = element.select_one('.asin_brand')
                            if asin_brand_element:
                                hidden_div = asin_brand_element.select_one('div[hidden]')
                                if hidden_div:
                                    self.log_message(f"ASIN {asin}: 品牌信息为空", level=LogLevel.DETAILED)
                                    empty_brand_count += 1
                            
                            # 检查品牌是否需要登录查看
                            if "****" in brand_text and "登录后可见" in element.get_text():
                                login_required_brand_count += 1
                            
                            # 获取卖家文本内容
                            seller_match = re.search(r'卖家[：:]\s*(.*?)[\n]', text)
                            seller = seller_match.group(1).lower().replace(' ', '') if seller_match else 'no'
                            sellers.append(seller)
                            
                            # 检查卖家是否需要登录查看
                            if "****" in seller and "登录后可见" in element.get_text():
                                login_required_seller_count += 1
                        
                        # 更新总需要登录查看的数量
                        total_login_required = login_required_brand_count + login_required_seller_count
                        
                        # 如果仍然需要登录，尝试重新加载页面
                        if total_login_required >= 5:
                            page_reload_count += 1
                            if page_reload_count < max_page_reloads:
                                self.log_message(f"第{page_reload_count}次页面刷新尝试，仍有品牌:{login_required_brand_count}个、卖家:{login_required_seller_count}个需要登录查看，总计:{total_login_required}个", always_show=True)
                                self.driver.refresh()
                                time.sleep(5)  # 等待页面加载
                                
                                # 处理验证码
                                if self.is_captcha_present():
                                    if not self.handle_captcha():
                                        self.log_message("重新加载页面后出现验证码，无法处理", always_show=True)
                                        continue
                            else:
                                self.log_message(f"经过{max_page_reloads}次页面刷新，仍有品牌:{login_required_brand_count}个、卖家:{login_required_seller_count}个需要登录查看，总计:{total_login_required}个，保存到超时表格", always_show=True)
                                self.save_timeout_asin(brand, brand)
                                return {"status": "error", "brand": brand, "error": "需要登录后查看内容"}
                        else:
                            self.log_message(f"经过{page_reload_count+1}次尝试，登录查看的元素已减少到品牌:{login_required_brand_count}个、卖家:{login_required_seller_count}个，总计:{total_login_required}个", always_show=True)
                            break

                # 如果存在品牌信息为空的情况，记录下来
                has_empty_brands = empty_brand_count > 0
                if has_empty_brands:
                    self.log_message(f"发现{empty_brand_count}个品牌信息为空的产品", always_show=True)

                # 确定品牌类型和是否符合条件
                # 默认为无品牌类型，符合条件
                brand_type = "no_brand"
                is_compliant = True
                
                # 这里可以根据实际业务逻辑设置brand_type和is_compliant
                # 例如，根据品牌名或其他条件判断
                if any("visit the" in b.lower() for b in brands if b != "no"):
                    brand_type = "visit_the"
                elif any(b != "no" for b in brands):
                    brand_type = "brand"
                
                # 返回提取的数据
                return {
                    "status": "success",
                    "brand": brand,
                    "brands_found": brands,
                    "sellers": sellers,
                    "has_empty_brands": has_empty_brands,  # 添加空品牌标志
                    "empty_brand_count": empty_brand_count,  # 添加空品牌数量
                    "brand_type": brand_type,
                    "is_compliant": is_compliant
                }
                
            except Exception as e:
                self.log_message(f"搜索品牌时出错: {str(e)}", always_show=True)
                if retry < max_retries - 1:
                    self.log_message(f"第{retry + 1}次尝试失败，{retry_interval}秒后重试", always_show=True)
                    time.sleep(retry_interval)
                else:
                    self.log_message(f"第{max_retries}次尝试失败，保存到超时表格", always_show=True)
                    self.save_timeout_asin(brand, brand)
                    return {"status": "error", "brand": brand, "error": str(e)}
        
        return {"status": "error", "brand": brand, "error": "达到最大重试次数"}
    
    def get_brand(self, asin):
        """获取指定ASIN的品牌信息"""
        try:
            product_url = f"https://www.amazon.{self.country_domain}/dp/{asin}?language=en_US"
            self.log_message(f"获取品牌信息: {product_url}")
            
            # 访问产品页面
            self.driver.get(product_url)
            time.sleep(random.uniform(1, 2))  # 随机等待时间
            
            # 处理验证码
            if self.is_captcha_present():
                if not self.handle_captcha():
                    self.log_message(f"无法处理验证码: {asin}")
                    return None, None, 0
            
            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "productTitle"))
                )
            except TimeoutException:
                self.log_message(f"页面加载超时: {asin}")
                return None, None, 0
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            brand_type = None
            brand = None
            
            # 提取品牌信息
            brand_element = soup.find('a', id='bylineInfo')
            if brand_element:
                brand_text = brand_element.text.strip()
                if "Visit the" in brand_text:
                    brand = brand_text.replace("Visit the", "").replace("Store", "").strip()
                    brand_type = "Visit the"
                else:
                    # 处理多种品牌前缀：英文的"Brand:"和中文/日文的"品牌："
                    brand = brand_text.replace("Brand:", "").replace("品牌：", "").strip()
                    brand_type = "Brand:"
            
            # 获取卖家数量信息
            seller_count = 0
            seller_info = soup.find('div', {'id': 'olp_feature_div'}) or soup.find('div', {'id': 'mbc'})
            if seller_info:
                seller_text = seller_info.text
                # 匹配卖家数量
                seller_match = re.search(r'(\d+)\s+(?:new|used)(?:\s+from)', seller_text)
                if seller_match:
                    seller_count = int(seller_match.group(1))
                    self.log_message(f"ASIN {asin}: 找到 {seller_count} 个卖家", level=LogLevel.DETAILED)
                else:
                    # 可能只有一个卖家
                    if '1 new' in seller_text.lower() or 'new from' in seller_text.lower():
                        seller_count = 1
                        self.log_message(f"ASIN {asin}: 找到 1 个卖家", level=LogLevel.DETAILED)
            
            self.log_message(f"ASIN {asin} 品牌类型: {brand_type}, 品牌: {brand}, 卖家数: {seller_count}", level=LogLevel.DETAILED)
            return brand_type, brand, seller_count
            
        except Exception as e:
            self.log_message(f"获取品牌信息时出错: {str(e)}", level=LogLevel.NORMAL)
            return None, None, 0
    
    def check_and_fix_location(self):
        """检查并修复位置设置，直接在当前页面进行操作"""
        try:
            # 获取当前位置文本
            try:
                current_location = self.driver.find_element(By.ID, "glow-ingress-line2").text.strip()
                self.log_message(f"当前位置: {current_location}", always_show=True)
            except Exception as e:
                self.log_message(f"获取位置信息出错: {str(e)}，将尝试设置位置", always_show=True)
                current_location = ""
            
            # 根据不同国家检查位置是否正确
            if self.country == "美国":
                if "98032" in current_location or "Kent 98032" in current_location:
                    self.log_message(f"当前位置已经是正确的美国位置: {current_location}", always_show=True)
                    return True
                else:
                    self.log_message(f"当前美国位置不正确，需要重新设置", always_show=True)
                    self.set_us_location(stay_on_page=True)
            elif self.country == "日本":
                if "060-8588" in current_location:
                    self.log_message(f"当前位置已经是正确的日本位置: {current_location}", always_show=True)
                    return True
                else:
                    self.log_message(f"当前日本位置不正确，需要重新设置", always_show=True)
                    self.set_japan_location(stay_on_page=True)
            elif self.country == "加拿大":
                if "B3H" in current_location and "0A" in current_location:
                    self.log_message(f"当前位置已经是正确的加拿大位置: {current_location}", always_show=True)
                    return True
                else:
                    self.log_message(f"当前加拿大位置不正确，需要重新设置", always_show=True)
                    self.set_canada_location(stay_on_page=True)
            else:
                self.log_message(f"未知国家: {self.country}，使用默认的美国位置", always_show=True)
                self.set_us_location(stay_on_page=True)
                
            return True
        except Exception as e:
            self.log_message(f"检查和修复位置时出错: {str(e)}", always_show=True)
            return False
    
    def get_product_info(self, asin):
        """获取Amazon产品详细信息"""
        result = {
            "asin": asin,
            "brand": None,
            "brand_type": None,
            "title": None,
            "has_variants": False,
            "is_out_of_stock": False,
            "is_unique_brand": False,
            "image_url": None,
            "dimensions": None,
            "weight": None,
            "rating": None,
            "rating_count": None,
            "sales_rank": [],
            "timeout": False,
            "seller_count": 0,  # 添加卖家数量字段
            "reason": None      # 保存不符合条件的原因
        }
        
        try:
            product_url = f"https://www.amazon.{self.country_domain}/dp/{asin}?language=en_US"
            self.driver.get(product_url)
            time.sleep(random.uniform(1, 2))  # 随机等待时间
            
            # 处理验证码
            if self.is_captcha_present():
                if not self.handle_captcha():
                    self.log_message(f"无法处理验证码: {asin}")
                    result["timeout"] = True
                    result["reason"] = "无法处理验证码"
                    self.save_timeout_asin(asin, None)
                    return result
            
            # 检查商品是否存在（检测错误页面）
            try:
                not_found_elem = self.driver.find_element(By.ID, "g")
                if not_found_elem:
                    self.log_message(f"ASIN {asin}: 商品不存在（检测到错误页面）", always_show=True)
                    result["reason"] = "商品不存在"
                    # 不将此ASIN记录为超时，因为它确实是不存在的
                    return result
            except Exception:
                # 元素不存在，说明不是错误页面，可以继续处理
                pass
                
            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.ID, "productTitle"))
                )
            except TimeoutException:
                self.log_message(f"页面加载超时，跳过ASIN: {asin}", always_show=True)
                result["timeout"] = True
                result["reason"] = "页面加载超时"
                self.save_timeout_asin(asin, "")
                self.save_result_realtime(result, is_qualified=False)
                return result
                
            # 检查商品不存在页面 - 方法1：检查ID为g的元素
            if self.driver.find_elements(By.ID, "g"):
                self.log_message(f"ASIN {asin}: 商品不存在(方法1)，跳过", always_show=True)
                result["reason"] = "商品不存在"
                self.save_result_realtime(result, is_qualified=False)
                return result
                
            # 检查商品不存在页面 - 方法2：检查带有猫图片的错误消息
            try:
                kitty_images = self.driver.find_elements(By.XPATH, "//img[contains(@src, 'kailey-kitty')]")
                if kitty_images:
                    if "在寻找某商品" in self.driver.page_source or "Looking for something" in self.driver.page_source:
                        self.log_message(f"ASIN {asin}: 商品不存在(方法2)，跳过", always_show=True)
                        result["reason"] = "商品不存在"
                        self.save_result_realtime(result, is_qualified=False)
                        return result
            except Exception:
                # 如果检查失败，继续处理
                pass
                
            # 每2个ASIN检查一次位置设置，增加检查频率
            if random.randint(1, 2) == 1:
                self.log_message(f"在产品页面上检查{self.country}位置设置", always_show=True)
                # 直接检查和修复位置，不需要跳转到主页
                self.check_and_fix_location()
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 提取产品标题
            title_elem = soup.select_one("#productTitle")
            if title_elem:
                result["title"] = title_elem.text.strip()
            
            # 提取品牌和品牌类型
            brand_element = soup.find('a', id='bylineInfo')
            if not brand_element:
                self.log_message(f"ASIN {asin}: 无法找到品牌元素，跳过", always_show=True)
                result["reason"] = "无法找到品牌元素"
                self.save_result_realtime(result, is_qualified=False)
                return result
                
            brand_text = brand_element.text.strip()
            if "Visit the" in brand_text:
                result["brand"] = brand_text.replace("Visit the", "").replace("Store", "").strip()
                result["brand_type"] = "Visit the"
                self.visit_the_brand_asins.append({
                    "asin": asin,
                    "brand": result["brand"],
                    "title": result["title"]
                })
            else:
                # 处理多种品牌前缀：英文的"Brand:"和中文/日文的"品牌："
                result["brand"] = brand_text.replace("Brand:", "").replace("品牌：", "").strip()
                result["brand_type"] = "Brand:"
                self.other_brand_asins.append({
                    "asin": asin,
                    "brand": result["brand"],
                    "title": result["title"]
                })
            
            self.log_message(f"找到品牌: {result['brand']} (类型: {result['brand_type']})", always_show=True)
            
            # 检查是否断货
            out_of_stock_elem = soup.find('div', id='outOfStock')
            if out_of_stock_elem and "Currently unavailable" in out_of_stock_elem.text:
                self.log_message(f"ASIN {asin}: 产品断货", always_show=True)
                result["is_out_of_stock"] = True
                
                # 检查断货产品是否有变体
                variant_elements = soup.find_all('div', id=lambda x: x and x.startswith('dimension-slot-info-'))
                if len(variant_elements) > 1:
                    self.log_message(f"ASIN {asin}: 断货产品有多个变体，归类为不符合条件", always_show=True)
                    result["has_variants"] = True
                    result["reason"] = "断货产品有多个变体"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 提取其他额外信息
                self.extract_additional_info(soup, result)
                
                # 断货状态下，检查评论数是否符合条件
                if result["rating_count"] is None or result["rating_count"] < 20 or result["rating_count"] > 2000:
                    self.log_message(f"ASIN {asin}: 断货产品评论数 {result['rating_count']} 不在要求范围内(20-2000)，归类为不符合条件", always_show=True)
                    result["reason"] = f"断货产品评论数 {result['rating_count']} 不在要求范围内"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 符合条件，搜索并判断品牌
                self.log_message(f"ASIN {asin}: 断货产品评论数符合条件，开始搜索判断品牌", always_show=True)
                
                # 调用analyze_brand_on_amazon搜索品牌
                brand_search_result = self.analyze_brand_on_amazon(result["brand"], result["brand_type"])
                if brand_search_result and "category" in brand_search_result:
                    result["brand_category"] = brand_search_result["category"]
                    result["search_result"] = brand_search_result
                    self.log_message(f"ASIN {asin}: 断货产品品牌分类结果: {result['brand_category']}", always_show=True)
                    
                    # 添加判断，如果分类为0（未找到匹配品牌），设置is_unique_brand为True，避免后续再进行品牌唯一性检查
                    if result["brand_category"] == 0:
                        result["is_unique_brand"] = True
                        self.log_message(f"ASIN {asin}: 未找到匹配品牌，自动设置为唯一品牌", always_show=True)
                    
                    self.save_result_realtime(result, is_qualified=True)
                    return result
                else:
                    self.log_message(f"ASIN {asin}: 断货产品品牌搜索失败", always_show=True)
                    result["reason"] = "品牌搜索失败"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
            else:
                self.log_message(f"ASIN {asin}: 产品有货", always_show=True)
                result["is_out_of_stock"] = False
                
                # 检查是否有变体
                variant_elements = soup.find_all('div', id=lambda x: x and x.startswith('dimension-slot-info-'))
                if len(variant_elements) > 1:
                    self.log_message(f"ASIN {asin}: 有货产品有多个变体，归类为不符合条件", always_show=True)
                    result["has_variants"] = True
                    result["reason"] = "有货产品有多个变体"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 检查卖家是否与品牌匹配
                seller_element = None
                for elem in soup.find_all("span", id="sellerProfileTriggerId"):
                    seller_element = elem
                    break
                
                if seller_element:
                    seller_name = seller_element.text.strip().lower()
                    brand_name = result["brand"].lower()
                    
                    # 清理品牌名和卖家名用于比较（去除特殊字符和空格）
                    seller_name_clean = re.sub(r'[^a-zA-Z0-9]', '', seller_name)
                    brand_name_clean = re.sub(r'[^a-zA-Z0-9]', '', brand_name)
                    
                    self.log_message(f"ASIN {asin}: 卖家: {seller_name}, 品牌: {brand_name}", always_show=True)
                    
                    if seller_name_clean in brand_name_clean or brand_name_clean in seller_name_clean:
                        self.log_message(f"ASIN {asin}: 卖家名与品牌名匹配，归类为不符合条件", always_show=True)
                        result["reason"] = f"有货产品卖家({seller_name})与品牌({brand_name})匹配"
                        self.save_result_realtime(result, is_qualified=False)
                        return result
                
                # 提取其他额外信息
                self.extract_additional_info(soup, result)
                
                # 有货状态下，检查评论数是否符合条件
                if result["rating_count"] is None or result["rating_count"] < 20 or result["rating_count"] > 2000:
                    self.log_message(f"ASIN {asin}: 有货产品评论数 {result['rating_count']} 不在要求范围内(20-2000)，归类为不符合条件", always_show=True)
                    result["reason"] = f"有货产品评论数 {result['rating_count']} 不在要求范围内"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
                
                # 符合条件，搜索并判断品牌
                self.log_message(f"ASIN {asin}: 有货产品评论数符合条件，开始搜索判断品牌", always_show=True)
                
                # 调用analyze_brand_on_amazon搜索品牌
                brand_search_result = self.analyze_brand_on_amazon(result["brand"], result["brand_type"])
                if brand_search_result and "category" in brand_search_result:
                    result["brand_category"] = brand_search_result["category"]
                    result["search_result"] = brand_search_result
                    self.log_message(f"ASIN {asin}: 有货产品品牌分类结果: {result['brand_category']}", always_show=True)
                    
                    # 添加判断，如果分类为0（未找到匹配品牌），设置is_unique_brand为True，避免后续再进行品牌唯一性检查
                    if result["brand_category"] == 0:
                        result["is_unique_brand"] = True
                        self.log_message(f"ASIN {asin}: 未找到匹配品牌，自动设置为唯一品牌", always_show=True)
                    
                    self.save_result_realtime(result, is_qualified=True)
                    return result
                else:
                    self.log_message(f"ASIN {asin}: 有货产品品牌搜索失败", always_show=True)
                    result["reason"] = "品牌搜索失败"
                    self.save_result_realtime(result, is_qualified=False)
                    return result
            
        except Exception as e:
            self.log_message(f"处理ASIN {asin}时出错: {str(e)}", level=LogLevel.NORMAL)
            result["timeout"] = True
            result["reason"] = f"处理出错: {str(e)}"
            # 实时保存超时的结果
            self.save_timeout_asin(asin, result.get("brand", ""))
            self.save_result_realtime(result, is_qualified=False)
            return result

    def extract_additional_info(self, soup, result):
        """从页面提取其他额外信息"""
        try:
            # 提取主图URL
            try:
                # 尝试从页面上的JavaScript数据中提取图片URL
                image_script = None
                for script in soup.find_all('script', type='text/javascript'):
                    if 'ImageBlockATF' in script.text and 'colorImages' in script.text:
                        image_script = script.text
                        break
                
                if image_script:
                    # 提取colorImages部分
                    match = re.search(r'colorImages\':\s*{\s*\'initial\':\s*(\[.*?\])', image_script, re.DOTALL)
                    if match:
                        image_data_str = match.group(1)
                        # 清理JavaScript对象使其成为有效的JSON
                        image_data_str = re.sub(r'(\w+):', r'"\1":', image_data_str)
                        image_data_str = re.sub(r'\'', '"', image_data_str)
                        
                        try:
                            image_data = json.loads(image_data_str)
                            if image_data and len(image_data) > 0 and 'hiRes' in image_data[0]:
                                result['image_url'] = image_data[0]['hiRes']
                            elif image_data and len(image_data) > 0 and 'large' in image_data[0]:
                                result['image_url'] = image_data[0]['large']
                        except json.JSONDecodeError:
                            # 尝试提取直接URL
                            img_url_match = re.search(r'"hiRes":\s*"(https://[^"]+)"', image_script)
                            if img_url_match:
                                result['image_url'] = img_url_match.group(1)
                            else:
                                img_url_match = re.search(r'"large":\s*"(https://[^"]+)"', image_script)
                                if img_url_match:
                                    result['image_url'] = img_url_match.group(1)
                
                # 如果上面方法失败，尝试从图片标签获取
                if not result['image_url']:
                    main_image = soup.find('img', id='landingImage') or soup.find('img', id='imgBlkFront')
                    if main_image and 'src' in main_image.attrs:
                        result['image_url'] = main_image['src']
                    elif main_image and 'data-old-hires' in main_image.attrs:
                        result['image_url'] = main_image['data-old-hires']
                
            except Exception as e:
                self.log_message(f"提取图片URL时出错: {str(e)}")
                
            # 提取尺寸信息
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and ('Dimensions' in th.text or 'Product Dimensions' in th.text):
                    dimensions_elem = tr.find('td')
                    if dimensions_elem:
                        result["dimensions"] = dimensions_elem.text.strip()
                    break
            
            # 提取重量信息
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and 'Weight' in th.text:
                    weight_elem = tr.find('td')
                    if weight_elem:
                        result["weight"] = weight_elem.text.strip()
                    break
            
            # 提取评分
            rating_elem = soup.select_one("span[data-hook='rating-out-of-text'], span.a-icon-alt")
            if rating_elem:
                rating_text = rating_elem.text
                rating_match = re.search(r'([\d.]+) out of', rating_text)
                if rating_match:
                    result["rating"] = float(rating_match.group(1))
                else:
                    # 尝试其他可能的格式
                    rating_match = re.search(r'([\d.]+)', rating_text)
                    if rating_match:
                        result["rating"] = float(rating_match.group(1))
            
            # 如果上面的方法没找到评分，尝试其他选择器
            if result["rating"] is None:
                for span in soup.find_all('span'):
                    if span.get('aria-label') and 'out of 5 stars' in span.get('aria-label'):
                        rating_text = span.get('aria-label')
                        rating_match = re.search(r'([\d.]+) out of', rating_text)
                        if rating_match:
                            result["rating"] = float(rating_match.group(1))
                            break
            
            # 提取评论数量
            reviews_elem = soup.select_one("span#acrCustomerReviewText")
            if reviews_elem:
                reviews_text = reviews_elem.text
                reviews_match = re.search(r'([\d,]+)', reviews_text)
                if reviews_match:
                    result["rating_count"] = int(reviews_match.group(1).replace(',', ''))
            
            # 如果上面的方法没找到评论数，尝试其他选择器
            if result["rating_count"] is None:
                for a in soup.find_all('a', {'id': 'acrCustomerReviewLink'}):
                    reviews_text = a.text
                    reviews_match = re.search(r'([\d,]+)', reviews_text)
                    if reviews_match:
                        result["rating_count"] = int(reviews_match.group(1).replace(',', ''))
                        break
            
            # 检查评论数是否在要求范围内
            if result["rating_count"] is not None:
                if result["rating_count"] < 20 or result["rating_count"] > 2000:
                    self.log_message(f"ASIN {result['asin']}: 评论数 {result['rating_count']} 不在要求范围内(20-2000)，跳过", always_show=True)
                    self.non_compliant_asins.append({
                        "asin": result["asin"],
                        "reason": f"评论数 {result['rating_count']} 不在要求范围内"
                    })
                    return False
            
            # 提取销售排名
            rank_section = None
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and ('Best Sellers Rank' in th.text or 'Sellers Rank' in th.text):
                    rank_section = tr.find('td')
                    break
                    
            if rank_section:
                # 提取排名文本
                rank_text = rank_section.text.strip()
                self.log_message(f"销售排名原始文本: {rank_text}")
                # 提取每个排名
                rank_matches = re.finditer(r'#([\d,]+) in ([^#\(]+)', rank_text)
                for match in rank_matches:
                    rank_num = int(match.group(1).replace(',', ''))
                    category = match.group(2).strip()
                    result["sales_rank"].append({
                        "rank": rank_num,
                        "category": category
                    })
                    
                if result["sales_rank"]:
                    self.log_message(f"找到销售排名: {result['sales_rank']}")
                    
            return True
                    
        except Exception as e:
            self.log_message(f"提取附加信息时出错: {str(e)}")
            return False 

    def save_brand_results_by_category(self, only_save_new_results=False):
        """根据品牌类型、符合条件情况和分类，将结果保存到不同的Excel文件中
           only_save_new_results: 为True时只保存新处理的结果，不读取并合并已有文件的内容
        """
        try:
            # 定义品牌分类名称
            category_names = {
                0: "品牌找不到",
                1: "品牌独占",
                2: "两个不重复卖家",
                3: "3-5个不重复卖家",
                4: "其他情况",
                5: "品牌信息为空",
                6: "网络超时或加载失败"
            }
            
            # 定义品牌类型中文名称
            brand_type_names = {
                "visit_the": "Visit_the类型",
                "brand": "Brand类型",
                "no_brand": "无品牌类型"
            }
            
            # 保存符合条件的结果（按品牌类型和分类保存）
            for brand_type, categories in self.brand_results.items():
                # 确保categories是字典类型
                if not isinstance(categories, dict):
                    self.log_message(f"跳过非字典类型的分类数据: {brand_type}", always_show=True)
                    continue
                    
                brand_type_cn = brand_type_names.get(brand_type, f"未知类型-{brand_type}")
                
                # 检查compliant键是否存在和是否为字典类型
                if "compliant" in categories and isinstance(categories["compliant"], dict):
                    for category, items in categories["compliant"].items():
                        if items:  # 只保存有数据的类别
                            category_name = category_names.get(category, f"未知分类-{category}")
                            
                            # 根据是否缺货，选择对应的文件夹
                            has_stock_items = []
                            out_of_stock_items = []
                            
                            # 按库存状态分类
                            for item in items:
                                if item.get("is_out_of_stock", False):
                                    out_of_stock_items.append(item)
                                else:
                                    has_stock_items.append(item)
                            
                            # 判断分类是否为"品牌信息为空"（分类 5）
                            if category == 5:
                                # 按原始分类分组
                                empty_brand_by_original = {}
                                for item in out_of_stock_items + has_stock_items:
                                    original_cat = item.get("original_category", 4)  # 默认为4如果没有原始分类
                                    if original_cat not in empty_brand_by_original:
                                        empty_brand_by_original[original_cat] = {"out_of_stock": [], "has_stock": []}
                                    
                                    if item.get("is_out_of_stock", False):
                                        empty_brand_by_original[original_cat]["out_of_stock"].append(item)
                                    else:
                                        empty_brand_by_original[original_cat]["has_stock"].append(item)
                                
                                # 保存到"品牌为空"文件夹下，使用与"符合"文件夹相同的逻辑
                                for original_cat, stock_items in empty_brand_by_original.items():
                                    original_cat_name = category_names.get(original_cat, f"未知分类-{original_cat}")
                                    
                                    # 保存缺货项目
                                    if stock_items["out_of_stock"]:
                                        folder = os.path.join(self.condition_folders["品牌为空"], "缺货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["out_of_stock"], filename, brand_type_cn, category_name, True, True, only_save_new_results)
                                    
                                    # 保存有货项目
                                    if stock_items["has_stock"]:
                                        folder = os.path.join(self.condition_folders["品牌为空"], "有货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["has_stock"], filename, brand_type_cn, category_name, False, True, only_save_new_results)
                            # 判断分类是否为"网络超时或加载失败"（分类 6）
                            elif category == 6:
                                # 按原始分类分组（如果没有原始分类，默认为6本身）
                                timeout_by_original = {}
                                for item in out_of_stock_items + has_stock_items:
                                    original_cat = item.get("original_category", 6)  # 默认为6如果没有原始分类
                                    if original_cat not in timeout_by_original:
                                        timeout_by_original[original_cat] = {"out_of_stock": [], "has_stock": []}
                                    
                                    if item.get("is_out_of_stock", False):
                                        timeout_by_original[original_cat]["out_of_stock"].append(item)
                                    else:
                                        timeout_by_original[original_cat]["has_stock"].append(item)
                                
                                # 保存到"网络超时"文件夹下
                                for original_cat, stock_items in timeout_by_original.items():
                                    original_cat_name = category_names.get(original_cat, f"未知分类-{original_cat}")
                                    
                                    # 保存缺货项目
                                    if stock_items["out_of_stock"]:
                                        folder = os.path.join(self.condition_folders["网络超时"], "缺货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["out_of_stock"], filename, brand_type_cn, category_name, True, True, only_save_new_results)
                                    
                                    # 保存有货项目
                                    if stock_items["has_stock"]:
                                        folder = os.path.join(self.condition_folders["网络超时"], "有货", brand_type_cn)
                                        os.makedirs(folder, exist_ok=True)
                                        filename = os.path.join(folder, f"{original_cat_name}.xlsx")
                                        self._save_items_to_excel(stock_items["has_stock"], filename, brand_type_cn, category_name, False, True, only_save_new_results)
                            else:
                                # 保存到"符合"文件夹下，正常逻辑
                                # 保存缺货项目
                                if out_of_stock_items:
                                    # 使用文件夹结构
                                    folder = os.path.join(self.condition_folders["符合"], "缺货", brand_type_cn)
                                    os.makedirs(folder, exist_ok=True)
                                    filename = os.path.join(folder, f"{category_name}.xlsx")
                                    
                                    self._save_items_to_excel(out_of_stock_items, filename, brand_type_cn, category_name, True, True, only_save_new_results)
                                
                                # 保存有货项目
                                if has_stock_items:
                                    # 使用文件夹结构
                                    folder = os.path.join(self.condition_folders["符合"], "有货", brand_type_cn)
                                    os.makedirs(folder, exist_ok=True)
                                    filename = os.path.join(folder, f"{category_name}.xlsx")
                                    
                                    self._save_items_to_excel(has_stock_items, filename, brand_type_cn, category_name, False, True, only_save_new_results)
            
            # 收集所有不符合条件的结果
            all_non_compliant_items = []
            
            # 收集各品牌类型下的不符合条件结果
            for brand_type, categories in self.brand_results.items():
                if "non_compliant" in categories and isinstance(categories["non_compliant"], list):
                    brand_type_cn = brand_type_names.get(brand_type, f"未知类型-{brand_type}")
                    for item in categories["non_compliant"]:
                        # 给每个项目添加品牌类型标记
                        item["品牌类型"] = brand_type_cn
                        all_non_compliant_items.append(item)
            
            # 保存所有不符合条件的结果到一个文件
            if all_non_compliant_items:
                folder = os.path.join(self.condition_folders["不符合"])
                os.makedirs(folder, exist_ok=True)
                filename = os.path.join(folder, "不符合条件ASIN.xlsx")
                
                self._save_items_to_excel(all_non_compliant_items, filename, "", "", None, False, only_save_new_results)
                self.log_message(f"已将所有不符合条件的ASIN({len(all_non_compliant_items)}个)保存到一个文件", level=LogLevel.SIMPLE)
            
            # 保存完整结果到一个文件（方便总览）
            self.save_comprehensive_results()
            
            # 保存超时ASIN
            if self.timeout_asins:
                timeout_df = pd.DataFrame(self.timeout_asins)
                output_file = os.path.join(self.results_folder, "超时ASIN.xlsx")
                timeout_df.to_excel(output_file, index=False)
                self.log_message(f"已保存{len(self.timeout_asins)}个超时ASIN到: {output_file}", level=LogLevel.SIMPLE)
            
            # 保存非唯一品牌的ASIN
            if self.non_unique_brand_asins:
                non_unique_df = pd.DataFrame(self.non_unique_brand_asins)
                output_file = os.path.join(self.results_folder, "非唯一品牌ASIN.xlsx")
                non_unique_df.to_excel(output_file, index=False)
                self.log_message(f"已保存{len(self.non_unique_brand_asins)}个非唯一品牌ASIN到: {output_file}", level=LogLevel.SIMPLE)
                    
        except Exception as e:
            self.log_message(f"保存结果到Excel时出错: {str(e)}", always_show=True)
            traceback.print_exc()
    
    def _save_items_to_excel(self, items, filename, brand_type_cn, category_name, is_out_of_stock, is_compliant, only_save_new_results=False):
        """辅助方法：保存项目到Excel文件"""
        try:
            # 检查是否仅保存新结果
            if only_save_new_results and os.path.exists(filename):
                # 读取已有的Excel文件
                try:
                    existing_df = pd.read_excel(filename)
                    # 如果存在ASIN列，提取已有的ASIN列表
                    if 'asin' in existing_df.columns:
                        existing_asins = set(existing_df['asin'].str.upper().tolist())
                        # 过滤出新的ASIN
                        new_items = [item for item in items if item.get('asin', '').upper() not in existing_asins]
                        
                        if not new_items:
                            self.log_message(f"所有结果已存在于文件中，无需更新: {filename}", level=LogLevel.SIMPLE)
                            return
                            
                        self.log_message(f"文件已存在，将追加{len(new_items)}个新结果到: {filename}", level=LogLevel.SIMPLE)
                        items = new_items
                except Exception as e:
                    self.log_message(f"读取已有Excel文件出错，将覆盖文件: {str(e)}", level=LogLevel.NORMAL)
            
            # 使用深拷贝避免修改原始数据
            import copy
            export_items = copy.deepcopy(items)
            
            # 处理复杂数据类型为字符串格式
            for item in export_items:
                # 销售排名处理
                if 'sales_rank' in item and item['sales_rank']:
                    # 提取排名和类别信息到单独的列
                    ranks = []
                    categories_list = []
                    for rank_info in item['sales_rank']:
                        if isinstance(rank_info, dict):
                            if 'rank' in rank_info:
                                ranks.append(str(rank_info['rank']))
                            if 'category' in rank_info:
                                categories_list.append(rank_info['category'])
                    
                    item['sales_rank_numbers'] = ';'.join(ranks)
                    item['sales_rank_categories'] = ';'.join(categories_list)
                    item['sales_rank'] = str(item['sales_rank'])
                
                # 品牌分析结果处理
                if 'brand_analysis' in item and item['brand_analysis']:
                    # 提取品牌分析的关键字段
                    if isinstance(item['brand_analysis'], dict):
                        analysis = item['brand_analysis']
                        item['brand_total_products'] = analysis.get('total_products', 0)
                        item['brand_sellers_count'] = len(analysis.get('unique_sellers', []))
                        item['brand_sellers'] = ';'.join(analysis.get('unique_sellers', []))
                        item['brand_category'] = analysis.get('category', 0)
                        # 将完整的分析结果转为字符串保存
                        item['brand_analysis'] = str(analysis)
                
                # 确保所有字段都有值
                for field in ['title', 'brand', 'brand_type', 'image_url', 
                            'dimensions', 'weight', 'rating', 'rating_count']:
                    if field not in item or item[field] is None:
                        item[field] = ""
                        
                # 添加分类分组信息
                item['品牌类型'] = brand_type_cn
                item['品牌分类'] = category_name if category_name else ""
                item['符合条件'] = "是" if is_compliant else "否"
                if is_out_of_stock is not None:
                    item['库存状态'] = "缺货" if is_out_of_stock else "有货"
                if not is_compliant and 'non_compliant_reason' in item:
                    item['不符合原因'] = item['non_compliant_reason']
            
            # 创建DataFrame
            df = pd.DataFrame(export_items)
            
            # 将新数据追加到现有文件或创建新文件
            if only_save_new_results and os.path.exists(filename) and 'existing_df' in locals():
                # 合并数据框
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                # 保存合并后的数据框
                combined_df.to_excel(filename, index=False)
                self.log_message(f"已将{len(items)}个新结果追加到: {filename}", level=LogLevel.SIMPLE)
            else:
                # 保存为新文件
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                df.to_excel(filename, index=False)
                self.log_message(f"已将{len(items)}个结果保存到: {filename}", level=LogLevel.SIMPLE)
                
        except Exception as e:
            self.log_message(f"保存Excel时出错: {str(e)}", always_show=True)
            traceback.print_exc()
    
    def save_comprehensive_results(self):
        """创建汇总结果"""
        try:
            # 创建汇总文件夹
            comprehensive_folder = self.results_folder
                
            # 创建全部结果文件
            all_results_file = os.path.join(comprehensive_folder, "全部结果.xlsx")
            
            # 收集所有结果到一个列表中
            all_results = []
            
            # 遍历所有品牌类型和分类
            for brand_type, categories in self.brand_results.items():
                # 符合条件的结果
                for category, results in categories.get("compliant", {}).items():
                    for result in results:
                        all_results.append({
                            "asin": result.get("asin"),
                            "title": result.get("title"),
                            "brand": result.get("brand"),
                            "brand_type": result.get("brand_type"),
                            "rating": result.get("rating"),
                            "rating_count": result.get("rating_count"),
                            "is_out_of_stock": result.get("is_out_of_stock", False),
                            "has_variants": result.get("has_variants", False),
                            "is_unique_brand": result.get("is_unique_brand", False),
                            "品牌类型": "Visit the" if brand_type == "visit_the" else ("Brand" if brand_type == "brand" else "无品牌"),
                            "符合条件": "是",
                            "库存状态": "缺货" if result.get("is_out_of_stock", False) else "有货",
                            "品牌分类": category
                        })
                
                # 不符合条件的结果
                for result in categories.get("non_compliant", []):
                    all_results.append({
                        "asin": result.get("asin"),
                        "title": result.get("title"),
                        "brand": result.get("brand"),
                        "brand_type": result.get("brand_type"),
                        "rating": result.get("rating"),
                        "rating_count": result.get("rating_count"),
                        "is_out_of_stock": result.get("is_out_of_stock", False),
                        "has_variants": result.get("has_variants", False),
                        "is_unique_brand": result.get("is_unique_brand", False),
                        "品牌类型": "Visit the" if brand_type == "visit_the" else ("Brand" if brand_type == "brand" else "无品牌"),
                        "符合条件": "否",
                        "不符合原因": result.get("non_compliant_reason", ""),
                        "品牌分类": ""
                    })
            
            # 创建数据框
            if all_results:
                # 将结果列表转换为DataFrame
                df = pd.DataFrame(all_results)
                
                # 保存到Excel
                with pd.ExcelWriter(all_results_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='全部结果', index=False)
                
                print(f"已保存{len(all_results)}个结果到汇总文件: {all_results_file}")
                self.log_message(f"已保存{len(all_results)}个结果到汇总文件: {all_results_file}", level=LogLevel.SIMPLE)
                
                # 创建数据分析文件
                pivot_file = os.path.join(comprehensive_folder, "数据分析.xlsx")
                with pd.ExcelWriter(pivot_file, engine='openpyxl') as writer:
                    # 总体分类统计
                    pivot_df = pd.pivot_table(
                        df, 
                        index=['品牌类型', '符合条件', '库存状态', '品牌分类'],
                        values=['asin'], 
                        aggfunc='count'
                    )
                    pivot_df.columns = ['数量']
                    pivot_df.to_excel(writer, sheet_name='分类统计')
                    
                    # 评论数量分布 - 使用副本避免SettingWithCopyWarning
                    if 'rating_count' in df.columns:
                        # 创建完整的数据副本
                        df_copy = df.copy()  
                        # 筛选有评论数的行
                        df_with_rating = df_copy[df_copy['rating_count'].notna()].copy()
                        
                        if not df_with_rating.empty:
                            # 确保rating_count是数值类型 - 使用安全的方式修改副本
                            df_with_rating['rating_count'] = pd.to_numeric(df_with_rating['rating_count'], errors='coerce')
                            
                            # 定义评论数量分组 - 使用安全的方式修改副本
                            df_with_rating['评论数分组'] = pd.cut(
                                df_with_rating['rating_count'],
                                bins=[0, 20, 50, 100, 200, 500, 1000, 2000, float('inf')],
                                labels=['<20', '20-50', '50-100', '100-200', '200-500', '500-1000', '1000-2000', '>2000']
                            )
                            
                            # 根据评论数量分组统计
                            ratings_pivot = pd.pivot_table(
                                df_with_rating,
                                index=['品牌类型', '符合条件', '库存状态', '评论数分组'],
                                values=['asin'],
                                aggfunc='count'
                            )
                            ratings_pivot.columns = ['数量']
                            ratings_pivot.to_excel(writer, sheet_name='评论数量分布')
                    
                self.log_message(f"已创建数据分析文件: {pivot_file}", level=LogLevel.SIMPLE)
                
        except Exception as e:
            self.log_message(f"创建汇总结果时出错: {str(e)}", always_show=True)

    def process_asin(self, asin):
        """处理单个ASIN，获取相关信息并进行分析"""
        self.log_message(f"开始处理ASIN: {asin}", level=LogLevel.NORMAL)
        
        try:
            # 获取产品信息
            result = self.get_product_info(asin)
            
            # 标记此ASIN已处理
            self.asin_counter += 1
            
            # 确定是否符合条件
            is_qualified = True
            non_compliant_reason = ""
            
            # 条件1: 产品必须缺货
            if not result.get("is_out_of_stock", False):
                is_qualified = False
                non_compliant_reason = "产品有货"
                self.log_message(f"ASIN {asin}: 产品有货，不符合条件", level=LogLevel.NORMAL)
            
            # 条件2: 产品不能有变体 - 检测到变体立即返回，不再进行其他条件检查
            elif result.get("has_variants", False):
                is_qualified = False
                non_compliant_reason = "产品有变体"
                self.log_message(f"ASIN {asin}: 产品有变体，不符合条件，跳过后续分析", level=LogLevel.NORMAL)
                
                # 记录不符合原因
                result["non_compliant_reason"] = non_compliant_reason
                
                # 实时保存结果
                self.save_result_realtime(result, is_qualified=False)
                
                # 每处理10个ASIN就保存一次断点
                if self.asin_counter % 10 == 0:
                    self.save_checkpoint()
                    self.log_message(f"已处理{self.asin_counter}个ASIN，保存断点", level=LogLevel.NORMAL)
                
                return result
            
            # 条件3: 评论数必须在20-2000之间
            elif result.get("rating_count") is not None and (result.get("rating_count") < 20 or result.get("rating_count") > 2000):
                is_qualified = False
                non_compliant_reason = f"评论数 {result.get('rating_count')} 不在要求范围内(20-2000)"
                self.log_message(f"ASIN {asin}: {non_compliant_reason}，不符合条件", level=LogLevel.NORMAL)
            
            # 条件4: 品牌必须唯一 - 除非品牌分类为0（未找到匹配品牌）
            elif not result.get("is_unique_brand", False):
                # 检查品牌分类结果，如果是0（未找到匹配品牌），则认为是唯一的
                if result.get("brand_category") == 0:
                    # 品牌分类为0，自动视为唯一品牌
                    result["is_unique_brand"] = True
                    self.log_message(f"ASIN {asin}: 品牌分类为0（未找到匹配品牌），自动视为唯一品牌", level=LogLevel.NORMAL)
                else:
                    # 品牌分类不为0，且不是唯一品牌，则不符合条件
                    is_qualified = False
                    non_compliant_reason = f"品牌 {result.get('brand', '')} 不是唯一的"
                    self.log_message(f"ASIN {asin}: {non_compliant_reason}，不符合条件", level=LogLevel.NORMAL)
            
            # 记录不符合原因
            if not is_qualified:
                result["non_compliant_reason"] = non_compliant_reason
            else:
                self.log_message(f"ASIN {asin}: 符合所有筛选条件", level=LogLevel.NORMAL)
            
            # 实时保存结果（使用新的方法，会按分类保存）
            self.save_result_realtime(result, is_qualified=is_qualified)
            
            # 每处理10个ASIN就保存一次断点
            if self.asin_counter % 10 == 0:
                self.save_checkpoint()
                self.log_message(f"已处理{self.asin_counter}个ASIN，保存断点", level=LogLevel.NORMAL)
            
            return result
        except Exception as e:
            self.log_message(f"处理ASIN {asin}时出错: {str(e)}", level=LogLevel.NORMAL)
            # 记录到超时ASIN列表
            self.timeout_asins.append({"asin": asin, "error": str(e)})
            
            # 标记为已处理
            self.asin_counter += 1
            
            # 创建最小结果对象
            error_result = {
                "asin": asin, 
                "error": str(e),
                "timeout": True,
                "brand_category": 5  # 网络超时或加载失败
            }
            
            # 实时保存错误结果
            self.save_result_realtime(error_result, is_qualified=False)
            
            return error_result

    def show_brand_seller_details(self, search_result):
        """显示详细的品牌和卖家信息"""
        try:
            if not search_result:
                return
                
            brand = search_result["brand"]
            brands_found = search_result["brands_found"]
            sellers = search_result["sellers"]
            
            self.log_message(f"\n品牌 '{brand}' 的详细搜索结果:", level=LogLevel.DETAILED)
            self.log_message("="*50, level=LogLevel.DETAILED)
            
            # 统计每个品牌出现的次数
            brand_counts = {}
            for b in brands_found:
                brand_counts[b] = brand_counts.get(b, 0) + 1
                
            # 统计每个卖家出现的次数
            seller_counts = {}
            for s in sellers:
                seller_counts[s] = seller_counts.get(s, 0) + 1
                
            # 显示品牌统计
            self.log_message("\n品牌统计:", level=LogLevel.DETAILED)
            for b, count in brand_counts.items():
                self.log_message(f"品牌: {b}, 出现次数: {count}", level=LogLevel.DETAILED)
                
            # 显示卖家统计
            self.log_message("\n卖家统计:", level=LogLevel.DETAILED)
            for s, count in seller_counts.items():
                self.log_message(f"卖家: {s}, 出现次数: {count}", level=LogLevel.DETAILED)
                
            # 显示品牌和卖家的对应关系
            self.log_message("\n品牌和卖家对应关系:", level=LogLevel.DETAILED)
            for i, (b, s) in enumerate(zip(brands_found, sellers)):
                self.log_message(f"第{i+1}条: 品牌={b}, 卖家={s}", level=LogLevel.DETAILED)
                
            self.log_message("="*50, level=LogLevel.DETAILED)
            
        except Exception as e:
            self.log_message(f"显示品牌卖家详细信息时出错: {str(e)}", level=LogLevel.NORMAL)

    def classify_brand_results(self, search_result):
        """根据搜索结果对品牌进行分类
        0: 品牌找不到
        1: 品牌独占
        2: 两个不重复卖家
        3: 3-5个不重复卖家
        4: 其他情况
        5: 包含空品牌信息
        """
        try:
            # 初始化原始分类变量
            original_category = None
            
            if not search_result or "status" not in search_result or search_result["status"] != "success":
                self.log_message(f"搜索失败或结果无效，归类为品牌未找到(0)", level=LogLevel.NORMAL)
                original_category = 0
                # 检查是否有空品牌信息
                if search_result and search_result.get("has_empty_brands", False):
                    search_result["original_category"] = original_category
                    self.log_message(f"同时发现{search_result.get('empty_brand_count', 0)}个品牌信息为空的产品，归类为有空品牌信息(5)", level=LogLevel.NORMAL)
                    return 5
                return original_category  # 品牌未找到
                
            # 提取品牌和卖家信息
            brands_found = search_result.get("brands_found", [])
            sellers = search_result.get("sellers", [])
            brand = search_result.get("brand", "").lower().replace(" ", "")  # 当前品牌，转小写并删除空格
            
            # 清理品牌名，去除特殊字符
            brand_clean = re.sub(r'[^a-zA-Z0-9]', '', brand)
            
            # 处理所有找到的品牌，转小写并删除空格
            cleaned_brands = []
            for b in brands_found:
                if b == "no":
                    cleaned_brands.append("no")
                else:
                    cleaned_brands.append(re.sub(r'[^a-zA-Z0-9]', '', b.lower().replace(" ", "")))
                    
            # 处理所有卖家，转小写并删除空格和特殊字符
            cleaned_sellers = []
            for s in sellers:
                if s == "no":
                    cleaned_sellers.append("no")
                else:
                    cleaned_sellers.append(re.sub(r'[^a-zA-Z0-9]', '', s.lower()))
            
            # 显示详细的品牌和卖家信息
            self.show_brand_seller_details(search_result)
            
            # 获取与当前品牌匹配的所有索引（对应JavaScript中的indices）
            matching_indices = [i for i, b in enumerate(cleaned_brands) if b == brand_clean]
            
            self.log_message(f"找到 {len(matching_indices)} 个匹配的品牌", level=LogLevel.NORMAL)
            
            # 先判断是否有匹配的品牌
            if len(matching_indices) == 0:
                self.log_message(f"未找到匹配品牌，归类为无匹配(0)", level=LogLevel.NORMAL)
                original_category = 0
                
                # 检查是否存在空品牌信息
                if search_result.get("has_empty_brands", False):
                    search_result["original_category"] = original_category
                    self.log_message(f"发现{search_result.get('empty_brand_count', 0)}个品牌信息为空的产品，归类为有空品牌信息(5)", level=LogLevel.NORMAL)
                    return 5  # 有空品牌信息
                    
                return original_category  # 品牌找不到
            
            # 获取匹配品牌对应的卖家
            matching_sellers = [cleaned_sellers[i] for i in matching_indices]
            
            # 判断卖家列表中是否包含品牌名称
            if brand_clean in matching_sellers:
                self.log_message(f"匹配品牌的卖家列表包含品牌名 '{brand_clean}'，归类为其他情况(4)", level=LogLevel.NORMAL)
                original_category = 4
                
                # 检查是否有空品牌
                if search_result.get("has_empty_brands", False):
                    search_result["original_category"] = original_category
                    return 5
                return original_category  # 其他情况：卖家包含品牌名
            
            # 根据匹配品牌的数量进行分类
            if len(matching_indices) > 5:
                self.log_message(f"匹配品牌超过5个，归类为其他情况(4)", level=LogLevel.NORMAL)
                original_category = 4
                
                # 检查是否有空品牌
                if search_result.get("has_empty_brands", False):
                    search_result["original_category"] = original_category
                    self.log_message(f"虽然匹配品牌超过5个(原始分类为4)，但发现空品牌信息，最终归类为5", level=LogLevel.NORMAL)
                    return 5
                return original_category  # 其他情况：匹配品牌超过5个
                
            elif len(matching_indices) > 2 and len(matching_indices) <= 5:
                # 检查是否有空品牌信息
                if search_result.get("has_empty_brands", False):
                    # 先判断原始类别
                    if len(set(matching_sellers)) == len(matching_sellers):
                        original_category = 3  # 3-5个不重复卖家
                    else:
                        original_category = 4  # 其他情况：有重复卖家
                        
                    search_result["original_category"] = original_category
                    self.log_message(f"虽然有{len(matching_indices)}个匹配品牌(原始分类为{original_category})，但发现{search_result.get('empty_brand_count', 0)}个品牌信息为空的产品，归类为有空品牌信息(5)", level=LogLevel.NORMAL)
                    return 5  # 有空品牌信息
                
                # 判断匹配品牌对应的卖家是否有重复
                if len(set(matching_sellers)) == len(matching_sellers):
                    self.log_message(f"3-5个匹配品牌的卖家不重复，归类为3", level=LogLevel.NORMAL)
                    return 3  # 3-5个不重复卖家
                else:
                    self.log_message(f"3-5个匹配品牌的卖家有重复，归类为其他情况(4)", level=LogLevel.NORMAL)
                    return 4  # 其他情况：有重复卖家
                    
            elif len(matching_indices) == 2:
                # 检查是否有空品牌信息
                if search_result.get("has_empty_brands", False):
                    # 先计算原始分类
                    idx1, idx2 = matching_indices
                    if cleaned_brands[idx1] == cleaned_brands[idx2] and cleaned_sellers[idx1] == cleaned_sellers[idx2]:
                        original_category = 4  # 其他情况：两个匹配品牌的卖家相同
                    else:
                        # 检查匹配品牌对应的卖家是否有amazon
                        if 'amazon' in matching_sellers[0] or 'amazon' in matching_sellers[1]:
                            original_category = 4  # 其他情况：有amazon卖家
                        else:
                            original_category = 2  # 两个不重复卖家
                    
                    search_result["original_category"] = original_category
                    self.log_message(f"虽然有两个匹配品牌(原始分类为{original_category})，但发现{search_result.get('empty_brand_count', 0)}个品牌信息为空的产品，归类为有空品牌信息(5)", level=LogLevel.NORMAL)
                    return 5  # 有空品牌信息
                
                idx1, idx2 = matching_indices
                if cleaned_brands[idx1] == cleaned_brands[idx2] and cleaned_sellers[idx1] == cleaned_sellers[idx2]:
                    self.log_message(f"两个匹配品牌和卖家完全相同，归类为其他情况(4)", level=LogLevel.NORMAL)
                    return 4  # 其他情况：两个匹配品牌的卖家相同
                else:
                    # 检查匹配品牌对应的卖家是否有amazon
                    if 'amazon' in matching_sellers[0] or 'amazon' in matching_sellers[1]:
                        self.log_message(f"两个匹配品牌的卖家中有amazon卖家，归类为其他情况(4)", level=LogLevel.NORMAL)
                        return 4  # 其他情况：有amazon卖家
                    else:
                        self.log_message(f"两个匹配品牌的卖家不相同且没有amazon卖家，归类为两个不重复卖家(2)", level=LogLevel.NORMAL)
                        return 2  # 两个不重复卖家
                        
            elif len(matching_indices) == 1:
                # 只有一个匹配的品牌
                # 也检查是否有空品牌信息
                if search_result.get("has_empty_brands", False):
                    # 先计算原始分类
                    if 'amazon' not in matching_sellers[0]:
                        original_category = 1  # 品牌独占
                    else:
                        original_category = 4  # 其他情况：amazon卖家
                        
                    search_result["original_category"] = original_category
                    self.log_message(f"虽然有单个匹配品牌(原始分类为{original_category})，但发现{search_result.get('empty_brand_count', 0)}个品牌信息为空的产品，归类为有空品牌信息(5)", level=LogLevel.NORMAL)
                    return 5  # 有空品牌信息
                
                if 'amazon' not in matching_sellers[0]:
                    self.log_message(f"只有一个匹配品牌且不是amazon卖家，归类为品牌独占(1)", level=LogLevel.NORMAL)
                    return 1  # 品牌独占
                else:
                    self.log_message(f"只有一个匹配品牌但是amazon卖家，归类为其他情况(4)", level=LogLevel.NORMAL)
                    return 4  # 其他情况：amazon卖家
                
        except Exception as e:
            self.log_message(f"分类品牌时出错: {str(e)}", level=LogLevel.NORMAL)
            original_category = 4  # 出错时默认分类为4
            
            # 检查是否有空品牌
            if search_result and search_result.get("has_empty_brands", False):
                search_result["original_category"] = original_category
                return 5
            return original_category

    def analyze_brand_on_amazon(self, brand, brand_type=None):
        """在Amazon上分析品牌，获取该品牌下的产品和卖家信息"""
        try:
            # 搜索品牌
            search_result = self.search_brand_on_amazon(brand)
            
            # 如果search_brand_on_amazon成功返回结果，就直接使用它
            if search_result.get("status") == "success":
                # 添加默认的品牌类型和符合条件标志
                # 如果传入了brand_type参数，使用传入的值，否则使用默认值
                if brand_type and brand_type in ["Visit the", "Brand:"]:
                    # 转换品牌类型格式
                    brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                    search_result["brand_type"] = brand_type_key
                else:
                    search_result["brand_type"] = "no_brand"  # 默认为无品牌类型
                
                search_result["is_compliant"] = True      # 默认为符合条件
                
                # 调用分类方法
                category = self.classify_brand_results(search_result)
                search_result["category"] = category
                
                # 记录分类信息和是否有空品牌
                has_empty_brands = search_result.get("has_empty_brands", False)
                if category == 5:
                    self.log_message(f"品牌 '{brand}' 含有空品牌信息，分类结果: {category}", always_show=True)
                elif has_empty_brands:
                    self.log_message(f"品牌 '{brand}' 含有空品牌信息，但因为品牌相同，分类结果为: {category}", always_show=True)
                else:
                    self.log_message(f"品牌 '{brand}' 分类结果: {category}", always_show=True)
                    
                return search_result
            
            # 如果search_brand_on_amazon未成功，根据状态分配默认分类
            if search_result.get("status") == "not_found":
                search_result["category"] = 0  # 品牌找不到
                # 添加默认的品牌类型和符合条件标志
                if brand_type and brand_type in ["Visit the", "Brand:"]:
                    brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                    search_result["brand_type"] = brand_type_key
                else:
                    search_result["brand_type"] = "no_brand"
                search_result["is_compliant"] = True
            else:
                search_result["category"] = 6  # 网络超时或加载失败（原为5）
                # 添加默认的品牌类型和符合条件标志
                if brand_type and brand_type in ["Visit the", "Brand:"]:
                    brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                    search_result["brand_type"] = brand_type_key
                else:
                    search_result["brand_type"] = "no_brand"
                search_result["is_compliant"] = True
            
            return search_result
            
        except Exception as e:
            self.log_message(f"分析品牌时出错: {str(e)}", always_show=True)
            result = {
                "status": "error", 
                "brand": brand, 
                "error": str(e), 
                "category": 6, 
                "is_compliant": True
            }
            
            # 添加品牌类型
            if brand_type and brand_type in ["Visit the", "Brand:"]:
                brand_type_key = "visit_the" if brand_type == "Visit the" else "brand"
                result["brand_type"] = brand_type_key
            else:
                result["brand_type"] = "no_brand"
                
            return result
    
    def worker(self):
        """工作线程，不断从队列中获取ASIN进行处理"""
        try:
            # 启动浏览器
            if not self.setup_selenium_browser():
                self.log_message("浏览器启动失败，线程退出", always_show=True)
                return
                
            # 首先访问亚马逊主页，然后设置位置
            max_location_attempts = 3
            location_set = False
            
            for attempt in range(max_location_attempts):
                try:
                    self.log_message(f"访问亚马逊{self.country}主页，尝试 {attempt+1}/{max_location_attempts}", always_show=True)
                    self.driver.get(f"https://www.amazon.{self.country_domain}/?language=en_US")
                    time.sleep(3)
                    
                    # 处理可能出现的验证码
                    if self.is_captcha_present():
                        if not self.handle_captcha():
                            self.log_message("无法解决主页验证码，继续尝试", always_show=True)
                    
                    # 检查并设置位置
                    location_set = self.check_and_fix_location()
                    if location_set:
                        if self.country == "美国":
                            self.log_message("成功设置美国位置为Kent 98032", always_show=True)
                        elif self.country == "日本":
                            self.log_message("成功设置日本位置为060-8588", always_show=True)
                        elif self.country == "加拿大":
                            self.log_message("成功设置加拿大位置为B3H 0A9", always_show=True)
                        else:
                            self.log_message(f"成功设置{self.country}位置", always_show=True)
                        break
                    else:
                        self.log_message(f"设置位置失败，尝试 {attempt+1}/{max_location_attempts}", always_show=True)
                        time.sleep(2)  # 等待2秒后重试
                except Exception as e:
                    self.log_message(f"设置初始环境时出错: {str(e)}", always_show=True)
                    time.sleep(2)  # 等待2秒后重试
            
            if not location_set:
                self.log_message(f"多次尝试设置{self.country}位置均失败，将使用默认位置继续", always_show=True)
                
            # 线程内ASIN处理计数
            local_asin_counter = 0
                
            # 处理队列中的ASIN
            while True:
                try:
                    # 从队列中获取ASIN，如果1秒内没有获取到，则退出
                    asin = self.asin_queue.get(timeout=1)
                    
                    if asin == "STOP":
                        self.log_message("收到停止信号，线程退出", always_show=True)
                        break
                        
                    self.log_message(f"开始处理ASIN: {asin}", always_show=True)
                    
                    # 使用锁确保线程安全
                    with self.lock:
                        # 检查ASIN是否已经处理过
                        if asin in self.processed_asins:
                            self.log_message(f"ASIN {asin} 已经处理过，跳过", always_show=True)
                            self.asin_queue.task_done()
                            continue
                    
                    # 重试处理ASIN，最多尝试3次
                    max_retry = 3
                    processed_success = False  # 标记该品牌是否已成功处理
                    
                    for retry in range(max_retry):
                        try:
                            self.process_asin(asin)
                            processed_success = True
                            break  # 成功处理，跳出重试循环
                        except Exception as e:
                            if retry < max_retry - 1:
                                self.log_message(f"处理ASIN {asin}失败，错误: {str(e)}，尝试 {retry+1}/{max_retry}", always_show=True)
                                time.sleep(2)  # 等待2秒后重试
                                
                                # 重新检查位置设置
                                self.check_and_fix_location()
                            else:
                                self.log_message(f"多次尝试处理ASIN {asin}均失败，错误: {str(e)}", always_show=True)
                                # 记录到超时ASIN列表
                                with self.lock:
                                    self.timeout_asins.append({"asin": asin, "error": str(e)})
                                    
                                    # 如果经过process_asin处理后还没添加到processed_asins，则在这里添加
                                    if asin not in self.processed_asins:
                                        self.processed_asins.add(asin)
                                        self.log_message(f"失败后手动将ASIN {asin} 添加到已处理集合", level=LogLevel.DETAILED)
                    
                    # 更新全局处理计数
                    with self.lock:
                        if hasattr(self, 'processed_count'):
                            self.processed_count += 1
                        
                        # 再次检查ASIN是否已添加到processed_asins
                        if asin not in self.processed_asins and processed_success:
                            self.processed_asins.add(asin)
                            self.log_message(f"成功后手动将ASIN {asin} 添加到已处理集合", level=LogLevel.DETAILED)
                    
                    # 增加线程内计数        
                    local_asin_counter += 1
                    
                    # 每处理10个ASIN就保存一次断点和结果
                    if local_asin_counter % 10 == 0:
                        with self.lock:
                            try:
                                self.log_message(f"线程已处理{local_asin_counter}个ASIN，保存断点和结果", always_show=True)
                                # 保存不符合条件的结果
                                self.save_brand_results_by_category()
                                # 保存断点
                                self.save_checkpoint(force=True)
                            except Exception as save_err:
                                self.log_message(f"保存断点或结果时出错: {str(save_err)}", always_show=True)
                    
                    # 标记队列任务完成
                    self.asin_queue.task_done()
                    
                except queue.Empty:
                    # 队列为空，退出循环
                    self.log_message("队列为空，线程退出", always_show=True)
                    break
                except Exception as e:
                    self.log_message(f"处理ASIN时出错: {str(e)}", always_show=True)
                    
        except Exception as e:
            self.log_message(f"工作线程出错: {str(e)}", always_show=True)
        finally:
            # 关闭浏览器
            self.close_selenium_browser()
    
    def process_asins_with_threads(self, asins, num_threads=5):
        """使用多线程处理ASIN列表"""
        try:
            # 创建线程池
            threads = []
            
            self.log_message(f"开始使用{num_threads}个线程处理{len(asins)}个ASIN", level=LogLevel.SIMPLE)
            
            # 先将所有ASIN放入队列
            for asin in asins:
                self.asin_queue.put(asin)
                
            # 创建并启动工作线程
            for _ in range(num_threads):
                t = threading.Thread(target=self.worker)
                t.daemon = True
                t.start()
                threads.append(t)
                
            # 等待所有ASIN处理完成
            self.asin_queue.join()
            
            # 向所有线程发送停止信号
            for _ in range(num_threads):
                self.asin_queue.put("STOP")
                
            # 等待所有线程结束
            for t in threads:
                t.join()
                
            self.log_message(f"所有ASIN处理完成，共处理了{len(asins)}个ASIN", level=LogLevel.SIMPLE)
            
            # 保存结果
            self.save_results_to_excel()
            
        except Exception as e:
            self.log_message(f"多线程处理ASIN时出错: {str(e)}", always_show=True)
    
    def run(self, asins_file=None, num_threads=1, skip_summary=False, resume=True, log_level=LogLevel.SIMPLE, headless_mode=True, country=None):
        """运行爬虫"""
        try:
            # 设置日志级别
            self.set_log_level(log_level)
            
            # 设置无头模式
            self.headless_mode = headless_mode
            self.log_message(f"无头模式状态: {'开启' if headless_mode else '关闭'}", always_show=True)
            
            # 设置国家
            if country:
                if country in self.countries:
                    self.country = country
                    self.country_domain = self.countries[country]["domain"]
                    self.country_zipcode = self.countries[country]["zipcode"]
                else:
                    self.log_message(f"不支持的国家: {country}，使用默认美国站点", always_show=True)
                    self.country = "美国"
                    self.country_domain = self.countries["美国"]["domain"]
                    self.country_zipcode = self.countries["美国"]["zipcode"]
            else:
                self.country = "美国"
                self.country_domain = self.countries["美国"]["domain"]
                self.country_zipcode = self.countries["美国"]["zipcode"]
                
            self.log_message(f"使用国家站点: {self.country}，域名: amazon.{self.country_domain}，邮编: {self.country_zipcode}", always_show=True)
            
            # 创建结果文件夹
            self.setup_results_folder(self.country)
            
            # 限制线程数为1（适用于32位Win7系统）
            if num_threads > 1 and self.system_info.get("is_win7", False) and not self.system_info.get("is_64bit", False):
                self.log_message(f"检测到32位Win7系统，将线程数从{num_threads}降为1以提高稳定性", always_show=True)
                num_threads = 1
            
            # 设置ASIN队列
            self.asin_queue = queue.Queue()
            
            # 标记是否已经运行
            self.is_running = True
            
            # 创建互斥锁，用于线程同步
            self.lock = threading.Lock()
            
            # 初始化计数器
            self.asin_counter = 0
            self.processed_count = 0
            
            # 初始化品牌搜索结果
            self.brand_results = {}  # 品牌类型 -> {"compliant": {分类1: [], 分类2: []}, "non_compliant": []}
            
            # 初始化不同品牌类型的字典
            for brand_type in ["visit_the", "brand", "no_brand"]:
                self.brand_results[brand_type] = {
                    "compliant": {
                        0: [],  # 品牌找不到
                        1: [],  # 品牌独占
                        2: [],  # 两个不重复卖家
                        3: [],  # 3-5个不重复卖家
                        4: [],  # 其他情况
                        5: []   # 品牌为空
                    },
                    "non_compliant": []
                }
            
            # 记录已处理的ASIN
            self.processed_asins = set()
            
            # 记录超时的ASIN
            self.timeout_asins = []
            
            # 记录非唯一品牌的ASIN
            self.non_unique_brand_asins = []
            
            # 记录visit_the类型的ASIN
            self.visit_the_brand_asins = []
            
            # 清理可能存在的残留数据
            if not resume:
                self.log_message("不恢复上次运行，丢弃已有断点", always_show=True)
            elif os.path.exists(self.checkpoint_file):
                self.log_message(f"尝试从断点恢复: {self.checkpoint_file}", always_show=True)
                if self.load_checkpoint():
                    self.log_message(f"成功从断点恢复，已处理{len(self.processed_asins)}个ASIN", always_show=True)
                else:
                    self.log_message("无法从断点恢复，将重新开始", always_show=True)
            
            # 加载ASIN列表
            if asins_file:
                asins = self.load_asins_from_excel(asins_file)
            else:
                self.log_message("未指定ASIN文件，尝试使用默认文件名", always_show=True)
                default_filename = f"amazon_results_{self.country}.xlsx"
                if os.path.exists(default_filename):
                    asins = self.load_asins_from_excel(default_filename)
                else:
                    self.log_message(f"默认文件 {default_filename} 不存在", always_show=True)
                    return False
            
            if not asins:
                self.log_message("ASIN列表为空，无法继续", always_show=True)
                return False
            
            # 仅处理尚未处理的ASIN
            pending_asins = [asin for asin in asins if asin not in self.processed_asins]
            self.log_message(f"加载了{len(asins)}个ASIN，其中{len(pending_asins)}个尚未处理", always_show=True)
            
            if not pending_asins:
                self.log_message("所有ASIN已处理完成", always_show=True)
                if not skip_summary:
                    self.save_brand_results_by_category()
                return True
                
            # 使用多线程处理ASIN
            self.process_asins_with_threads(pending_asins, num_threads)
            
            # 保存最终结果
            if not skip_summary:
                self.save_brand_results_by_category()
            
            # 标记运行完成
            self.is_running = False
            
            self.log_message("处理完成！", always_show=True)
            return True
            
        except Exception as e:
            self.log_message(f"运行出错: {str(e)}", always_show=True)
            traceback.print_exc()
            return False
    
    def save_results_to_excel(self):
        """将所有结果保存到Excel文件"""
        try:
            # 保存符合条件的结果
            if self.results:
                results_df = pd.DataFrame(self.results)
                
                # 处理销售排名列（含有列表的嵌套结构）
                if 'sales_rank' in results_df.columns:
                    results_df['sales_rank'] = results_df['sales_rank'].apply(lambda x: str(x) if x else "")
                
                # 保存到Excel
                output_file = os.path.join(self.results_folder, "Amazon产品信息.xlsx")
                results_df.to_excel(output_file, index=False)
                self.log_message(f"已保存{len(self.results)}个符合条件的产品信息到: {output_file}", level=LogLevel.SIMPLE)
            
            # 按类别保存结果
            self.save_brand_results_by_category()
                
        except Exception as e:
            self.log_message(f"保存结果到Excel时出错: {str(e)}", always_show=True)

    def set_us_location(self, stay_on_page=False):
        """设置美国位置
        
        Args:
            stay_on_page: 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        """
        try:
            self.log_message("开始设置美国位置...", always_show=True)
            driver = self.driver
            
            # 如果不是stay_on_page模式，访问Amazon主页，否则保持在当前页面
            if not stay_on_page:
                driver.get("https://www.amazon.com?language=en_US")
                time.sleep(3)
            
            # 检查当前位置是否已经是98032
            current_location = driver.find_element(By.ID, "glow-ingress-line2").text.strip()
            if "98032" in current_location or "Kent 98032" in current_location:
                self.log_message(f"当前位置已经是98032: {current_location}", always_show=True)
                return True
            
            # 检查是否需要处理验证码
            if self.is_captcha_present():
                self.log_message("检测到验证码，尝试处理", always_show=True)
                if not self.handle_captcha():
                    self.log_message("验证码处理失败，继续尝试设置位置", always_show=True)
            
            # 尝试多种方法找到并点击位置选择器
            location_clicked = False
            
            # 方法1: 通过ID找位置选择器
            try:
                location_element = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "nav-global-location-popover-link"))
                )
                location_element.click()
                location_clicked = True
                self.log_message("通过ID找到位置选择器并点击", always_show=True)
            except Exception as e:
                self.log_message(f"通过ID查找位置选择器失败: {str(e)}", always_show=True)
            
            # 方法2: 如果方法1失败，尝试通过通配符XPath找位置选择器
            if not location_clicked:
                try:
                    location_xpath = "//a[contains(@id, 'location') or contains(@class, 'location')]"
                    location_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, location_xpath))
                    )
                    location_element.click()
                    location_clicked = True
                    self.log_message("通过XPath找到位置选择器并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过XPath查找位置选择器失败: {str(e)}", always_show=True)
            
            # 方法3: 如果上述方法失败，尝试通过文本内容找位置选择器
            if not location_clicked:
                try:
                    location_text_xpath = "//*[contains(text(), 'Deliver to') or contains(text(), 'Delivering to')]"
                    location_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, location_text_xpath))
                    )
                    location_element.click()
                    location_clicked = True
                    self.log_message("通过文本内容找到位置选择器并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过文本内容查找位置选择器失败: {str(e)}", always_show=True)
            
            if not location_clicked:
                self.log_message("无法找到位置选择器，可能需要手动设置位置", always_show=True)
                return False
            
            # 等待位置弹窗加载
            time.sleep(2)
            
            # 尝试多种方法输入邮政编码
            zipcode_input_found = False
            
            # 方法1: 通过ID找邮政编码输入框
            try:
                zipcode_input = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdateInput"))
                )
                zipcode_input.clear()
                zipcode_input.send_keys("98032")  # 输入美国华盛顿州肯特市的邮编
                zipcode_input_found = True
                self.log_message("通过ID找到邮政编码输入框", always_show=True)
            except Exception as e:
                self.log_message(f"通过ID查找邮政编码输入框失败: {str(e)}", always_show=True)
            
            # 方法2: 如果方法1失败，尝试通过XPath找邮政编码输入框
            if not zipcode_input_found:
                try:
                    zipcode_xpath = "//input[contains(@id, 'GLUXZip') or contains(@placeholder, 'zip') or contains(@placeholder, 'code')]"
                    zipcode_input = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, zipcode_xpath))
                    )
                    zipcode_input.clear()
                    zipcode_input.send_keys("98032")
                    zipcode_input_found = True
                    self.log_message("通过XPath找到邮政编码输入框", always_show=True)
                except Exception as e:
                    self.log_message(f"通过XPath查找邮政编码输入框失败: {str(e)}", always_show=True)
            
            if not zipcode_input_found:
                self.log_message("无法找到邮政编码输入框，可能需要手动设置", always_show=True)
                return False
            
            # 尝试多种方法点击应用按钮
            apply_clicked = False
            
            # 方法1: 通过ID找应用按钮
            try:
                apply_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdate"))
                )
                apply_button.click()
                apply_clicked = True
                self.log_message("通过ID找到应用按钮并点击", always_show=True)
            except Exception as e:
                self.log_message(f"通过ID查找应用按钮失败: {str(e)}", always_show=True)
            
            # 方法2: 如果方法1失败，尝试通过XPath找应用按钮
            if not apply_clicked:
                try:
                    apply_xpath = "//input[@type='submit'] | //span[contains(text(), 'Apply')] | //button[contains(text(), 'Apply')]"
                    apply_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, apply_xpath))
                    )
                    apply_button.click()
                    apply_clicked = True
                    self.log_message("通过XPath找到应用按钮并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过XPath查找应用按钮失败: {str(e)}", always_show=True)
            
            # 方法3: 尝试直接按Enter键
            if not apply_clicked:
                try:
                    # 找回邮政编码输入框，并按Enter键
                    zipcode_input = driver.find_element(By.ID, "GLUXZipUpdateInput")
                    zipcode_input.send_keys(Keys.ENTER)
                    apply_clicked = True
                    self.log_message("通过Enter键提交", always_show=True)
                except Exception as e:
                    self.log_message(f"通过Enter键提交失败: {str(e)}", always_show=True)
            
            if not apply_clicked:
                self.log_message("无法找到应用按钮或提交方式，可能需要手动应用", always_show=True)
                return False
            
            # 等待位置更新
            time.sleep(3)
            
            # 确认位置或处理额外的弹窗
            try:
                # 查找可能存在的"Done"按钮
                done_buttons = driver.find_elements(By.XPATH, 
                    "//button[contains(text(), 'Done')] | //span[contains(text(), 'Done')]")
                if done_buttons:
                    done_buttons[0].click()
                    self.log_message("点击Done按钮确认位置", always_show=True)
                    time.sleep(2)
            except Exception as e:
                self.log_message(f"处理确认按钮时出错: {str(e)}", always_show=True)
            
            # 刷新页面以确保位置更新
            driver.refresh()
            time.sleep(3)
            
            # 检查位置是否已设置为98032
            try:
                # 根据提供的HTML结构，检查导航栏中的位置信息
                location_text = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.ID, "glow-ingress-line2"))
                ).text.strip()
                
                # 清理位置文本，移除不可见的Unicode字符
                location_text = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f]', '', location_text)
                
                self.log_message(f"当前位置显示为: {location_text}", always_show=True)
                
                if "Kent" in location_text and "98032" in location_text:
                    self.log_message("位置已成功设置为Kent 98032", always_show=True)
                    return True
                else:
                    self.log_message(f"位置设置不匹配，当前显示: {location_text}，将重试", always_show=True)
                    # 可以在这里添加重试逻辑，但为了避免无限循环，这里返回True允许继续执行
                    return True
            except Exception as e:
                self.log_message(f"验证位置时出错: {str(e)}", always_show=True)
                return True
            
        except Exception as e:
            self.log_message(f"设置位置时出错: {str(e)}", always_show=True)
            # 即使出错也返回True，允许继续执行
            return True

    def set_japan_location(self, stay_on_page=False):
        """设置日本位置
        
        Args:
            stay_on_page: 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        """
        try:
            self.log_message("开始设置日本位置...", always_show=True)
            driver = self.driver
            
            # 如果不是stay_on_page模式，访问Amazon日本主页，否则保持在当前页面
            if not stay_on_page:
                driver.get("https://www.amazon.co.jp?language=en_US")
                time.sleep(3)
            
            # 检查当前位置是否已经是060-8588
            current_location = driver.find_element(By.ID, "glow-ingress-line2").text.strip()
            if "060-8588" in current_location:
                self.log_message(f"当前位置已经是060-8588: {current_location}", always_show=True)
                return True
            
            # 检查是否需要处理验证码
            if self.is_captcha_present():
                self.log_message("检测到验证码，尝试处理", always_show=True)
                if not self.handle_captcha():
                    self.log_message("验证码处理失败，继续尝试设置位置", always_show=True)
            
            # 尝试多种方法找到并点击位置选择器
            location_clicked = False
            
            # 方法1: 通过ID找位置选择器
            try:
                location_element = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "nav-global-location-popover-link"))
                )
                location_element.click()
                location_clicked = True
                self.log_message("通过ID找到位置选择器并点击", always_show=True)
            except Exception as e:
                self.log_message(f"通过ID查找位置选择器失败: {str(e)}", always_show=True)
            
            # 方法2: 如果方法1失败，尝试通过通配符XPath找位置选择器
            if not location_clicked:
                try:
                    location_xpath = "//a[contains(@id, 'location') or contains(@class, 'location')]"
                    location_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, location_xpath))
                    )
                    location_element.click()
                    location_clicked = True
                    self.log_message("通过XPath找到位置选择器并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过XPath查找位置选择器失败: {str(e)}", always_show=True)
            
            # 方法3: 如果上述方法失败，尝试通过文本内容找位置选择器
            if not location_clicked:
                try:
                    location_text_xpath = "//*[contains(text(), 'Deliver to') or contains(text(), 'Delivering to')]"
                    location_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, location_text_xpath))
                    )
                    location_element.click()
                    location_clicked = True
                    self.log_message("通过文本内容找到位置选择器并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过文本内容查找位置选择器失败: {str(e)}", always_show=True)
            
            if not location_clicked:
                self.log_message("无法找到位置选择器，可能需要手动设置位置", always_show=True)
                return False
            
            # 等待位置弹窗加载
            time.sleep(2)
            
            # 日本邮编有两个输入框
            try:
                # 第一个输入框(前3位)
                zipcode_input1 = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdateInput_0"))
                )
                zipcode_input1.clear()
                zipcode_input1.send_keys("060")
                
                # 第二个输入框(后4位)
                zipcode_input2 = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdateInput_1"))
                )
                zipcode_input2.clear()
                zipcode_input2.send_keys("8588")
                
                self.log_message("成功输入日本邮编: 060-8588", always_show=True)
            except Exception as e:
                self.log_message(f"输入日本邮编时出错: {str(e)}", always_show=True)
                return False
            
            # 点击应用按钮
            try:
                apply_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdate"))
                )
                apply_button.click()
                self.log_message("点击应用按钮", always_show=True)
            except Exception as e:
                self.log_message(f"点击应用按钮时出错: {str(e)}", always_show=True)
                return False
            
            # 等待位置更新
            time.sleep(3)
            
            # 确认位置或处理额外的弹窗
            try:
                # 查找可能存在的"Done"按钮
                done_buttons = driver.find_elements(By.XPATH, 
                    "//button[contains(text(), 'Done')] | //span[contains(text(), 'Done')]")
                if done_buttons:
                    done_buttons[0].click()
                    self.log_message("点击Done按钮确认位置", always_show=True)
                    time.sleep(2)
            except Exception as e:
                self.log_message(f"处理确认按钮时出错: {str(e)}", always_show=True)
            
            # 刷新页面以确保位置更新
            driver.refresh()
            time.sleep(3)
            
            # 检查位置是否已设置为060-8588
            try:
                location_text = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.ID, "glow-ingress-line2"))
                ).text.strip()
                
                self.log_message(f"当前位置显示为: {location_text}", always_show=True)
                
                if "060-8588" in location_text:
                    self.log_message("位置已成功设置为日本 060-8588", always_show=True)
                    return True
                else:
                    self.log_message(f"位置设置不匹配，当前显示: {location_text}，将重试", always_show=True)
                    return True
            except Exception as e:
                self.log_message(f"验证位置时出错: {str(e)}", always_show=True)
                return True
            
        except Exception as e:
            self.log_message(f"设置日本位置时出错: {str(e)}", always_show=True)
            return True

    def set_canada_location(self, stay_on_page=False):
        """设置加拿大位置
        
        Args:
            stay_on_page: 如果为True，则尝试在当前页面设置位置，而不是跳转到主页
        """
        try:
            self.log_message("开始设置加拿大位置...", always_show=True)
            driver = self.driver
            
            # 如果不是stay_on_page模式，访问Amazon加拿大主页，否则保持在当前页面
            if not stay_on_page:
                driver.get("https://www.amazon.ca?language=en_US")
                time.sleep(3)
            
            # 检查当前位置是否已经是B3H 0A9
            current_location = driver.find_element(By.ID, "glow-ingress-line2").text.strip()
            if "B3H" in current_location and "0A" in current_location:
                self.log_message(f"当前位置已经是B3H 0A: {current_location}", always_show=True)
                return True
            
            # 检查是否需要处理验证码
            if self.is_captcha_present():
                self.log_message("检测到验证码，尝试处理", always_show=True)
                if not self.handle_captcha():
                    self.log_message("验证码处理失败，继续尝试设置位置", always_show=True)
            
            # 尝试多种方法找到并点击位置选择器
            location_clicked = False
            
            # 方法1: 通过ID找位置选择器
            try:
                location_element = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "nav-global-location-popover-link"))
                )
                location_element.click()
                location_clicked = True
                self.log_message("通过ID找到位置选择器并点击", always_show=True)
            except Exception as e:
                self.log_message(f"通过ID查找位置选择器失败: {str(e)}", always_show=True)
            
            # 方法2: 如果方法1失败，尝试通过通配符XPath找位置选择器
            if not location_clicked:
                try:
                    location_xpath = "//a[contains(@id, 'location') or contains(@class, 'location')]"
                    location_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, location_xpath))
                    )
                    location_element.click()
                    location_clicked = True
                    self.log_message("通过XPath找到位置选择器并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过XPath查找位置选择器失败: {str(e)}", always_show=True)
            
            # 方法3: 如果上述方法失败，尝试通过文本内容找位置选择器
            if not location_clicked:
                try:
                    location_text_xpath = "//*[contains(text(), 'Deliver to') or contains(text(), 'Delivering to')]"
                    location_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, location_text_xpath))
                    )
                    location_element.click()
                    location_clicked = True
                    self.log_message("通过文本内容找到位置选择器并点击", always_show=True)
                except Exception as e:
                    self.log_message(f"通过文本内容查找位置选择器失败: {str(e)}", always_show=True)
            
            if not location_clicked:
                self.log_message("无法找到位置选择器，可能需要手动设置位置", always_show=True)
                return False
            
            # 等待位置弹窗加载
            time.sleep(2)
            
            # 加拿大邮编有两个输入框
            try:
                # 第一个输入框(前3位)
                zipcode_input1 = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdateInput_0"))
                )
                zipcode_input1.clear()
                zipcode_input1.send_keys("B3H")
                
                # 第二个输入框(后2位)
                zipcode_input2 = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdateInput_1"))
                )
                zipcode_input2.clear()
                zipcode_input2.send_keys("0A9")
                
                self.log_message("成功输入加拿大邮编: B3H 0A9", always_show=True)
            except Exception as e:
                self.log_message(f"输入加拿大邮编时出错: {str(e)}", always_show=True)
                return False
            
            # 点击应用按钮
            try:
                apply_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "GLUXZipUpdate"))
                )
                apply_button.click()
                self.log_message("点击应用按钮", always_show=True)
            except Exception as e:
                self.log_message(f"点击应用按钮时出错: {str(e)}", always_show=True)
                return False
            
            # 等待位置更新
            time.sleep(3)
            
            # 确认位置或处理额外的弹窗
            try:
                # 查找可能存在的"Done"按钮
                done_buttons = driver.find_elements(By.XPATH, 
                    "//button[contains(text(), 'Done')] | //span[contains(text(), 'Done')]")
                if done_buttons:
                    done_buttons[0].click()
                    self.log_message("点击Done按钮确认位置", always_show=True)
                    time.sleep(2)
            except Exception as e:
                self.log_message(f"处理确认按钮时出错: {str(e)}", always_show=True)
            
            # 刷新页面以确保位置更新
            driver.refresh()
            time.sleep(3)
            
            # 检查位置是否已设置为B3H 0A
            try:
                location_text = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.ID, "glow-ingress-line2"))
                ).text.strip()
                
                self.log_message(f"当前位置显示为: {location_text}", always_show=True)
                
                if "B3H" in location_text and "0A" in location_text:
                    self.log_message("位置已成功设置为加拿大 B3H 0A", always_show=True)
                    return True
                else:
                    self.log_message(f"位置设置不匹配，当前显示: {location_text}，将重试", always_show=True)
                    return True
            except Exception as e:
                self.log_message(f"验证位置时出错: {str(e)}", always_show=True)
                return True
            
        except Exception as e:
            self.log_message(f"设置加拿大位置时出错: {str(e)}", always_show=True)
            return True

    def save_checkpoint(self, force=False):
        """保存断点续传数据到JSON文件"""
        try:
            # 如果没有新的处理，且不是强制保存，则跳过
            if self.asin_counter == 0 and not force:
                return
                
            # 保存完整的断点数据
            checkpoint_data = {
                "processed_count": self.processed_count,
                "brand_compliant_count": self.brand_compliant_count,
                "brand_noncompliant_count": self.brand_noncompliant_count,
                "compliant_brands": self.compliant_brands,
                "noncompliant_brands": self.noncompliant_brands,
                "processed_asins": list(self.processed_asins)  # 添加已处理的ASIN列表
            }
            
            # 保存到JSON文件
            with open(self.checkpoint_file, "w", encoding="utf-8") as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            self.log_message(f"断点续传数据已保存到: {self.checkpoint_file}", level=LogLevel.NORMAL)
        except Exception as e:
            self.log_message(f"保存断点续传数据时出错: {str(e)}", always_show=True)

    def load_checkpoint(self):
        """加载断点续传数据"""
        try:
            if not os.path.exists(self.checkpoint_file):
                self.log_message(f"没有找到断点续传文件: {self.checkpoint_file}，将从头开始处理", level=LogLevel.NORMAL)
                return False
                
            with open(self.checkpoint_file, "r", encoding="utf-8") as f:
                checkpoint_data = json.load(f)
                
            # 恢复各项数据
            self.processed_count = checkpoint_data.get("processed_count", 0)
            self.brand_compliant_count = checkpoint_data.get("brand_compliant_count", 0)
            self.brand_noncompliant_count = checkpoint_data.get("brand_noncompliant_count", 0)
            self.compliant_brands = checkpoint_data.get("compliant_brands", [])
            self.noncompliant_brands = checkpoint_data.get("noncompliant_brands", [])
            
            # 恢复已处理的ASIN列表
            processed_asins = checkpoint_data.get("processed_asins", [])
            self.processed_asins = set(processed_asins)
            
            self.log_message(f"已加载断点续传数据，上次处理到第 {self.processed_count} 个ASIN，共 {len(self.processed_asins)} 个已处理ASIN", level=LogLevel.NORMAL)
            return True
        except Exception as e:
            self.log_message(f"加载断点续传数据时出错: {str(e)}", always_show=True)
            # 出错时重置处理计数
            self.processed_count = 0
            return False
    
    def save_timeout_asin(self, asin, brand):
        """将超时的ASIN保存到专门的表格中"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.timeout_file):
                # 创建新表格
                df = pd.DataFrame(columns=["ASIN", "品牌", "时间"])
                df.to_excel(self.timeout_file, index=False)
            
            # 读取现有数据
            df = pd.read_excel(self.timeout_file)
            
            # 添加新数据
            new_row = {"ASIN": asin, "品牌": brand, "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            
            # 保存回文件
            df.to_excel(self.timeout_file, index=False)
            
            self.log_message(f"已将超时ASIN {asin} 保存到 {self.timeout_file}", level=LogLevel.DETAILED)
        except Exception as e:
            self.log_message(f"保存超时ASIN时出错: {str(e)}", level=LogLevel.NORMAL)
    
    def save_result_realtime(self, asin_data, is_qualified=False):
        """实时保存处理结果"""
        try:
            # 如果ASIN已经处理过，则跳过
            if asin_data["asin"] in self.processed_asins:
                self.log_message(f"跳过重复ASIN: {asin_data['asin']}", level=LogLevel.DETAILED)
                return
            
            # 添加到已处理ASIN集合中
            self.processed_asins.add(asin_data["asin"])
            
            # 品牌类型映射
            brand_type_map = {
                "Visit the": "visit_the",
                "Brand:": "brand",
                "": "no_brand"  # 无品牌
            }
            
            # 获取品牌类型和其他信息
            is_out_of_stock = asin_data.get("is_out_of_stock", False)
            brand_type = asin_data.get("brand_type", "")
            brand_type_key = brand_type_map.get(brand_type, "no_brand")
            
            # 使用传入的is_qualified参数，优先于asin_data中的is_compliant
            is_compliant = is_qualified if is_qualified else asin_data.get("is_compliant", False)
            
            # 获取品牌分类（如果有）
            brand_category = asin_data.get("brand_category", 4)  # 默认为类别4（其他情况）
            
            # 检查search_result中是否包含original_category并将其传递给asin_data
            if "search_result" in asin_data and isinstance(asin_data["search_result"], dict):
                if "original_category" in asin_data["search_result"]:
                    asin_data["original_category"] = asin_data["search_result"]["original_category"]
                    self.log_message(f"为ASIN {asin_data['asin']} 设置原始分类: {asin_data['original_category']}", level=LogLevel.DETAILED)
            
            # 初始化品牌类型对应的结果字典（如果不存在）
            if brand_type_key not in self.brand_results:
                self.brand_results[brand_type_key] = {
                    "compliant": {},
                    "non_compliant": []
                }
            
            # 根据是否符合条件分别处理
            if is_compliant:
                # 符合条件的ASIN，按分类保存
                if "compliant" not in self.brand_results[brand_type_key]:
                    self.brand_results[brand_type_key]["compliant"] = {}
                
                # 确保品牌分类键存在
                if brand_category not in self.brand_results[brand_type_key]["compliant"]:
                    self.brand_results[brand_type_key]["compliant"][brand_category] = []
                
                # 添加到对应分类中
                self.brand_results[brand_type_key]["compliant"][brand_category].append(asin_data)
                self.log_message(f"ASIN {asin_data['asin']} 已添加到符合条件分类 {brand_category}", level=LogLevel.NORMAL)
            else:
                # 不符合条件的ASIN
                if "non_compliant" not in self.brand_results[brand_type_key]:
                    self.brand_results[brand_type_key]["non_compliant"] = []
                
                # 添加到不符合条件列表
                self.brand_results[brand_type_key]["non_compliant"].append(asin_data)
                self.log_message(f"ASIN {asin_data['asin']} 已添加到不符合条件列表", level=LogLevel.NORMAL)
            
            # 每处理10个ASIN后，保存一次分类结果
            if len(self.processed_asins) % 10 == 0:
                self.save_brand_results_by_category()
                self.log_message(f"已处理 {len(self.processed_asins)} 个ASIN，保存分类结果", level=LogLevel.NORMAL)
                
        except Exception as e:
            self.log_message(f"保存结果时发生错误: {str(e)}", always_show=True)
            self.log_message(traceback.format_exc(), level=LogLevel.DETAILED)
    
    # 添加已处理ASIN集合的初始化（在__init__方法中）

    def add_result(self, brand, category, search_result=None):
        """添加品牌结果到相应类别"""
        result_data = {"品牌": brand}
        
        # 添加分类信息
        category_names = {
            0: "品牌未找到",
            1: "品牌独占",
            2: "两个不重复卖家",
            3: "3-5个不重复卖家",
            4: "其他情况",
            5: "品牌信息为空",
            6: "网络超时或加载失败"
        }
        result_data["分类"] = category_names.get(category, "未知")
        
        if search_result:
            # 添加更多搜索结果数据
            brands_found = search_result.get("brands_found", [])
            sellers = search_result.get("sellers", [])
            
            result_data.update({
                "匹配结果数": len(brands_found),
                "匹配品牌": ", ".join(brands_found[:5]),  # 最多显示前5个
                "卖家": ", ".join(sellers[:5]),  # 最多显示前5个
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        else:
            result_data["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 确定品牌类型
        brand_type = "no_brand"  # 默认为无品牌类型
        if search_result and "brand_type" in search_result:
            if search_result["brand_type"] == "visit_the":
                brand_type = "visit_the"
            elif search_result["brand_type"] == "brand":
                brand_type = "brand"
            
        # 判断是否符合条件的结果
        is_compliant = True  # 默认为符合条件
        if search_result and "is_compliant" in search_result:
            is_compliant = search_result["is_compliant"]
            
        # 存储到正确的位置
        if is_compliant:
            # 符合条件的结果按类别存储
            if category in self.brand_results[brand_type]["compliant"]:
                self.brand_results[brand_type]["compliant"][category].append(result_data)
            else:
                # 如果类别不存在，创建一个新类别列表
                self.brand_results[brand_type]["compliant"][category] = [result_data]
        else:
            # 不符合条件的结果直接添加到non_compliant列表
            self.brand_results[brand_type]["non_compliant"].append(result_data)
            
        self.log_message(f"品牌 '{brand}' 已分类为: {category}，品牌类型: {brand_type}，是否符合条件: {is_compliant}")
    
    def process_brands(self, brands, max_retries=3):
        """处理品牌列表"""
        total_brands = len(brands)
        self.log_message(f"开始处理{total_brands}个品牌...", always_show=True)
        
        # 用于记录已处理的品牌，防止重复处理
        already_processed = set()
        
        for i, brand in enumerate(brands):
            # 检查品牌是否已经处理过
            if brand in already_processed:
                self.log_message(f"品牌 '{brand}' 已经处理过，跳过", always_show=True)
                continue
                
            self.log_message(f"处理品牌 {i+1}/{total_brands}: {brand}", always_show=True)
            
            retries = 0
            processed = False  # 标记该品牌是否已成功处理
            
            while retries < max_retries and not processed:
                try:
                    # 搜索品牌
                    search_result = self.analyze_brand_on_amazon(brand)
                    
                    # 检查搜索结果状态
                    if search_result:
                        status = search_result.get("status", "unknown")
                        category = search_result.get("category", 5)  # 默认为5（超时或加载失败）
                        
                        if status == "success":
                            # 成功获取结果，进行分类
                            self.log_message(f"品牌 '{brand}' 分类结果: {category}", always_show=True)
                            self.add_result(brand, category, search_result)
                            
                            # 实时保存结果
                            self.save_brand_results_by_category()
                            self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                            
                            processed = True  # 标记为已处理成功
                            
                            # 随机等待以避免被检测为机器人
                            wait_time = random.uniform(2, 5)
                            self.log_message(f"等待 {wait_time:.2f} 秒后继续...")
                            time.sleep(wait_time)
                        elif status == "not_found":
                            # 品牌未找到，直接分类为0
                            self.log_message(f"品牌 '{brand}' 未找到，直接分类为0", always_show=True)
                            self.add_result(brand, 0, {"状态": "not_found"})
                            
                            # 实时保存结果
                            self.save_brand_results_by_category()
                            self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                            
                            processed = True  # 标记为已处理成功
                            
                            # 随机等待以避免被检测为机器人
                            wait_time = random.uniform(2, 5)
                            time.sleep(wait_time)
                        elif status in ["no_elements", "extraction_failed"]:
                            # 其他提取失败情况，重试
                            if retries == max_retries - 1:
                                self.log_message(f"品牌 '{brand}' 处理失败(状态: {status})，分类为未找到", always_show=True)
                                self.add_result(brand, 0, {"状态": status})
                                
                                # 实时保存结果
                                self.save_brand_results_by_category()
                                self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                                
                                processed = True  # 标记为已处理完成
                            else:
                                self.log_message(f"处理品牌 '{brand}' 失败(状态: {status})，重试 ({retries+1}/{max_retries})")
                                retries += 1
                                time.sleep(random.uniform(5, 10))
                        elif status in ["timeout", "no_sellers", "captcha_failed"]:
                            # 网络超时、无卖家信息或验证码问题，分类为6
                            if retries == max_retries - 1:
                                self.log_message(f"品牌 '{brand}' 处理超时(状态: {status})，放入超时分类", always_show=True)
                                self.add_result(brand, 6, {"状态": status})
                                
                                # 实时保存结果
                                self.save_brand_results_by_category()
                                self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                                
                                processed = True  # 标记为已处理完成
                            else:
                                self.log_message(f"处理品牌 '{brand}' 超时(状态: {status})，重试 ({retries+1}/{max_retries})")
                                retries += 1
                                time.sleep(random.uniform(5, 10))
                        else:
                            # 其他未知状态，分类为6
                            if retries == max_retries - 1:
                                self.log_message(f"品牌 '{brand}' 处理异常(状态: {status})，放入超时分类", always_show=True)
                                self.add_result(brand, 6, {"状态": status})
                                
                                # 实时保存结果
                                self.save_brand_results_by_category()
                                self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                                
                                processed = True  # 标记为已处理完成
                            else:
                                self.log_message(f"处理品牌 '{brand}' 异常(状态: {status})，重试 ({retries+1}/{max_retries})")
                                retries += 1
                                time.sleep(random.uniform(5, 10))
                    else:
                        # 搜索结果为空，重试或放入超时分类
                        if retries == max_retries - 1:
                            self.log_message(f"品牌 '{brand}' 处理返回空结果，放入超时分类", always_show=True)
                            self.add_result(brand, 6, {"状态": "empty_result"})
                            
                            # 实时保存结果
                            self.save_brand_results_by_category()
                            self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                            
                            processed = True  # 标记为已处理完成
                        else:
                            self.log_message(f"处理品牌 '{brand}' 返回空结果，重试 ({retries+1}/{max_retries})")
                            retries += 1
                            time.sleep(random.uniform(5, 10))
                
                except Exception as e:
                    self.log_message(f"处理品牌时出错: {str(e)}", always_show=True)
                    retries += 1
                    if retries >= max_retries:
                        # 达到最大重试次数，将品牌添加到超时类别
                        self.log_message(f"品牌 '{brand}' 处理出错，放入超时分类", always_show=True)
                        self.add_result(brand, 6, {"状态": "error", "错误": str(e)})
                        
                        # 实时保存结果
                        self.save_brand_results_by_category()
                        self.log_message(f"已实时保存品牌 '{brand}' 的处理结果", always_show=True)
                        
                        processed = True  # 标记为已处理完成
                    time.sleep(random.uniform(5, 10))
            
            # 无论处理成功与否，都将品牌标记为已处理，避免重复处理
            already_processed.add(brand)
            
        # 最终日志信息
        self.log_message("所有品牌处理完成", always_show=True)

    def load_brands_from_excel(self, filename="brands.xlsx"):
        """从Excel文件加载品牌列表"""
        try:
            df = pd.read_excel(filename)
            brands = []
            
            # 检查列名 - 不区分大小写
            columns = [col.lower() for col in df.columns]
            
            if 'brand' in columns or '品牌' in columns:
                # 获取原始列名（保留大小写）
                if 'brand' in columns:
                    original_col = df.columns[columns.index('brand')]
                else:
                    original_col = df.columns[columns.index('品牌')]
                
                brands = df[original_col].dropna().tolist()
                # 清理品牌名称，去掉空格和其他不必要的字符
                brands = [str(brand).strip() for brand in brands if str(brand).strip()]
                self.log_message(f"成功从Excel的'{original_col}'列加载了{len(brands)}个品牌", always_show=True)
            else:
                # 查找任何包含"brand"或"品牌"的列（不区分大小写）
                brand_cols = [col for col in df.columns if 'brand' in col.lower() or '品牌' in col.lower()]
                if brand_cols:
                    # 使用第一个匹配的列
                    brands = df[brand_cols[0]].dropna().tolist()
                    # 清理品牌名称
                    brands = [str(brand).strip() for brand in brands if str(brand).strip()]
                    self.log_message(f"成功从Excel的'{brand_cols[0]}'列加载了{len(brands)}个品牌", always_show=True)
                else:
                    # 如果找不到合适的列名，尝试使用第一列
                    if len(df.columns) > 0:
                        first_col = df.columns[0]
                        brands = df[first_col].dropna().tolist()
                        # 清理品牌名称
                        brands = [str(brand).strip() for brand in brands if str(brand).strip()]
                        self.log_message(f"未找到品牌列，使用第一列'{first_col}'加载了{len(brands)}个项目", always_show=True)
                    else:
                        self.log_message(f"Excel文件为空或没有列", always_show=True)
                        return []
            
            return brands
        except Exception as e:
            self.log_message(f"从Excel文件加载品牌时出错: {str(e)}", always_show=True)
            return []

    def execute_cdp_commands(self):
        """执行CDP命令绕过自动化检测"""
        try:
            # 设置用户代理
            user_agent = self.get_random_user_agent()
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": user_agent,
                "platform": "Windows" if "Windows" in user_agent else "macOS"
            })
            self.log_message("已通过CDP设置用户代理", level=LogLevel.DETAILED)
            
            # 隐藏webdriver属性
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                '''
            })
            self.log_message("已隐藏webdriver属性", level=LogLevel.DETAILED)
            
            # 隐藏自动化相关特征
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({ state: Notification.permission }) :
                            originalQuery(parameters)
                    );
                    
                    // 修改navigator.plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => {
                            return {
                                length: 5,
                                item: () => { return {}; },
                                namedItem: () => { return {}; },
                                refresh: () => {}
                            };
                        }
                    });
                    
                    // 修改navigator.languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en-US', 'en']
                    });
                    
                    // 创建一个假的WebGL
                    const getParameter = WebGLRenderingContext.getParameter;
                    WebGLRenderingContext.prototype.getParameter = function(parameter) {
                        if (parameter === 37445) {
                            return 'Intel Open Source Technology Center';
                        }
                        if (parameter === 37446) {
                            return 'Mesa DRI Intel(R) HD Graphics (Skylake GT2)';
                        }
                        return getParameter.apply(this, arguments);
                    };
                '''
            })
            self.log_message("已隐藏多个自动化特征", level=LogLevel.DETAILED)
            
            # 屏蔽关于"chrome.automation"的可能检测
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };
                '''
            })
            self.log_message("已模拟chrome对象", level=LogLevel.DETAILED)
            
            return True
        except Exception as e:
            self.log_message(f"执行CDP命令时出错: {str(e)}", level=LogLevel.DETAILED)
            return False


def main():
    """主函数"""
    try:
        # 创建爬虫实例
        scraper = AmazonAsinScraper()
        
        # 设置日志级别
        scraper.set_log_level(LogLevel.NORMAL)
        
        # 运行爬虫
        scraper.run()
        
        return True
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        logger.error("程序运行出错", exc_info=True)
        return False


def create_gui():
    """创建GUI界面，使用精简版主题适合客户使用"""
    root = tk.Tk()
    
    # 设置应用程序图标和任务栏图标
    try:
        # 先设置应用程序ID - 这会帮助Windows正确识别应用并更新任务栏图标
        if platform.system() == "Windows":
            try:
                import ctypes
                app_id = "AmazonAsinScraper.App.1.0"  # 唯一应用ID
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
            except Exception:
                pass
        
        # 设置窗口图标
        icon_path = "icon.ico"
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
            
            # 在Windows上使用多种方法设置任务栏图标
            if platform.system() == "Windows":
                try:
                    import ctypes
                    # 获取窗口根句柄 - 更可靠的方法
                    hwnd = ctypes.windll.user32.GetForegroundWindow()
                    
                    # 常量定义
                    ICON_SMALL = 0
                    ICON_BIG = 1
                    WM_SETICON = 0x0080
                    LR_LOADFROMFILE = 0x0010
                    IMAGE_ICON = 1
                    
                    # 使用绝对路径
                    abs_icon_path = os.path.abspath(icon_path)
                    
                    # 加载小图标
                    h_icon_small = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 16, 16, LR_LOADFROMFILE
                    )
                    if h_icon_small:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                    
                    # 加载大图标
                    h_icon_big = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 32, 32, LR_LOADFROMFILE
                    )
                    if h_icon_big:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                    
                    # 刷新窗口以应用更改
                    ctypes.windll.user32.UpdateWindow(hwnd)
                    
                    # 在窗口显示后再次设置图标 - 使用事件回调
                    def set_icon_after_visible():
                        try:
                            hwnd = ctypes.windll.user32.GetForegroundWindow()
                            if h_icon_small:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                            if h_icon_big:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                        except Exception:
                            pass
                    
                    # 在100ms后执行以确保窗口已完全加载
                    root.after(100, set_icon_after_visible)
                except Exception:
                    pass
    except:
        pass  # 忽略图标设置错误
    
    # 使用统一UI主题设置窗口
    AmazonUITheme.setup_window(
        root,
        "亚马逊产品筛选工具", 
        size="900x650", 
        resizable=(True, True)
    )
    
    # 应用样式
    AmazonUITheme.setup_styles()
    
    # 创建标题栏
    title_frame = ttk.Frame(root)
    title_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
    
    title_label = ttk.Label(
        title_frame, 
        text="亚马逊产品筛选工具",
        font=("微软雅黑", 16, "bold"),
        foreground=AmazonUITheme.COLORS["primary"]
    )
    title_label.pack(side=tk.LEFT)
    
    # 添加横向分隔线
    separator = ttk.Separator(root, orient=tk.HORIZONTAL)
    separator.pack(fill=tk.X, padx=15, pady=5)
    
    # 主框架
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
    
    # 创建左右分栏
    left_frame = ttk.Frame(main_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 10))
    
    right_frame = ttk.Frame(main_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # ===== 左侧配置区域 =====
    
    # 国家选择
    country_frame = ttk.LabelFrame(left_frame, text="国家设置", padding=10)
    country_frame.pack(fill=tk.X, pady=(0, 10))
    
    # 国家变量
    country_var = tk.StringVar(value="美国")
    
    # 国家选择下拉框
    ttk.Label(country_frame, text="选择国家:").pack(anchor=tk.W, pady=(0, 5))
    country_combo = ttk.Combobox(
        country_frame, 
        textvariable=country_var, 
        values=["美国", "日本", "加拿大"], 
        state="readonly",
        width=15
    )
    country_combo.pack(fill=tk.X, pady=5)
    
    # 文件选择框架
    file_frame = ttk.LabelFrame(left_frame, text="ASIN列表文件", padding=10)
    file_frame.pack(fill=tk.X, pady=10)
    
    # 文件路径变量
    file_path_var = tk.StringVar()
    
    # 文件选择说明标签
    ttk.Label(
        file_frame, 
        text="导入包含ASIN列表的Excel文件:",
        foreground=AmazonUITheme.COLORS["primary"],
        font=("微软雅黑", 9, "bold")
    ).pack(anchor=tk.W, pady=(0, 5))
    
    # 文件路径显示
    file_entry_frame = ttk.Frame(file_frame)
    file_entry_frame.pack(fill=tk.X, pady=5)
    
    file_path_entry = ttk.Entry(file_entry_frame, textvariable=file_path_var, width=30)
    file_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
    
    # 初始化默认文件名
    default_filename = f"amazon_results_{country_var.get()}.xlsx"
    file_path_var.set(default_filename)
    
    # 添加国家选择变化时的回调函数，更新默认文件名
    def on_country_change(*args):
        selected_country = country_var.get()
        default_filename = f"amazon_results_{selected_country}.xlsx"
        file_path_var.set(default_filename)
        
    # 绑定国家选择变化事件
    country_var.trace_add("write", on_country_change)
    
    def browse_file():
        """浏览文件按钮回调函数"""
        selected_country = country_var.get()
        default_filename = f"amazon_results_{selected_country}.xlsx"
        
        file_path = filedialog.askopenfilename(
            initialdir=".", 
            title=f"选择包含{selected_country}的ASINs的Excel文件",
            filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))
        )
        if file_path:
            file_path_var.set(file_path)
            status_label.config(text=f"已选择文件: {os.path.basename(file_path)}")
    
    # 导入按钮
    import_btn = ttk.Button(
        file_entry_frame, 
        text="导入文件", 
        command=browse_file,
        width=15
    )
    import_btn.pack(side=tk.RIGHT)
    
    # 文件格式提示
    file_info_frame = ttk.Frame(file_frame)
    file_info_frame.pack(fill=tk.X, pady=(5, 0))
    
    ttk.Label(
        file_info_frame, 
        text="支持的格式: Excel文件(.xlsx)，包含ASIN列或Amazon Links列",
        font=("微软雅黑", 8),
        foreground=AmazonUITheme.COLORS["light_text"]
    ).pack(anchor=tk.W)
    
    # 启动按钮框架
    button_frame = ttk.Frame(left_frame)
    button_frame.pack(fill=tk.X, pady=15)
    
    # 启动按钮
    start_button = ttk.Button(
        button_frame, 
        text="开始处理", 
        command=lambda: start_processing(),
        width=15
    )
    start_button.pack(side=tk.RIGHT, padx=5)
    
    # 退出按钮
    exit_button = ttk.Button(
        button_frame, 
        text="退出", 
        command=root.destroy,
        width=15
    )
    exit_button.pack(side=tk.LEFT, padx=5)
    
    # ===== 右侧日志区域 =====
    log_frame = ttk.LabelFrame(right_frame, text="运行日志", padding=10)
    log_frame.pack(fill=tk.BOTH, expand=True)
    
    log_text = tk.Text(log_frame, wrap=tk.WORD, height=20)
    log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    scrollbar = ttk.Scrollbar(log_text, orient="vertical", command=log_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    log_text.configure(yscrollcommand=scrollbar.set)
    
    # 状态栏
    status_frame = ttk.Frame(root)
    status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=5)
    
    status_label = ttk.Label(
        status_frame, 
        text="准备就绪",
        font=("微软雅黑", 9),
        foreground=AmazonUITheme.COLORS["light_text"]
    )
    status_label.pack(side=tk.LEFT)
    
    # 设置这些变量为默认值（后台隐藏设置）
    threads_var = tk.IntVar(value=1)           # 默认1个线程
    skip_summary_var = tk.BooleanVar(value=True)  # 默认跳过汇总
    resume_var = tk.BooleanVar(value=True)     # 默认启用断点续传
    log_level_var = tk.IntVar(value=0)         # 默认简易日志
    headless_var = tk.BooleanVar(value=True)   # 默认无头模式
    progress_var = tk.DoubleVar()              # 进度值
    
    def start_processing():
        """开始处理按钮回调函数"""
        excel_file = file_path_var.get()
        if not excel_file:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
            
        if not os.path.exists(excel_file):
            messagebox.showerror("错误", f"文件不存在: {excel_file}")
            return
        
        # 使用默认设置
        num_threads = threads_var.get()          # 固定为1
        skip_summary = skip_summary_var.get()    # 固定为True
        resume = resume_var.get()                # 固定为True
        log_level = log_level_var.get()          # 固定为简易(0)
        headless_mode = headless_var.get()       # 固定为True
        selected_country = country_var.get()
        
        status_label.config(text="处理中...")
        progress_var.set(0)
        
        # 禁用按钮，避免重复点击
        start_button.configure(state="disabled")
        import_btn.configure(state="disabled")
        
        # 使用线程执行处理，避免UI卡死
        def processing_thread():
            try:
                # 创建爬虫实例
                scraper = AmazonAsinScraper()
                
                # 设置选定的国家
                scraper.country = selected_country
                scraper.country_domain = scraper.countries[selected_country]["domain"]
                scraper.country_zipcode = scraper.countries[selected_country]["zipcode"]
                
                scraper.log_message(f"使用国家: {selected_country}, 域名: {scraper.country_domain}", always_show=True)
                
                # 配置日志输出到GUI
                # 创建自定义日志处理器，将日志输出到Text控件
                class TextHandler(logging.Handler):
                    def __init__(self, text_widget):
                        logging.Handler.__init__(self)
                        self.text_widget = text_widget
                        
                    def emit(self, record):
                        msg = self.format(record)
                        
                        def append():
                            self.text_widget.configure(state='normal')
                            self.text_widget.insert(tk.END, msg + '\n')
                            self.text_widget.see(tk.END)  # 自动滚动到最新日志
                            self.text_widget.configure(state='disabled')
                            
                        # 使用afterQueue避免线程问题
                        if not root.winfo_exists():
                            return  # 如果窗口已关闭，不执行操作
                        self.text_widget.after(0, append)
                
                # 添加Text控件处理器到日志
                text_handler = TextHandler(log_text)
                text_handler.setFormatter(logging.Formatter(LOG_FORMAT))
                logger.addHandler(text_handler)
                
                # 在运行前再次强制设置国家
                scraper.country = selected_country
                
                # 运行爬虫
                scraper.run(
                    asins_file=excel_file, 
                    num_threads=num_threads, 
                    skip_summary=skip_summary,
                    resume=resume,
                    log_level=log_level,
                    headless_mode=headless_mode,
                    country=selected_country
                )
                
                # 更新UI
                def update_ui():
                    if not root.winfo_exists():
                        return  # 如果窗口已关闭，不执行操作
                    status_label.config(text="处理完成")
                    messagebox.showinfo("完成", "所有ASIN处理完成")
                    # 恢复按钮状态
                    start_button.configure(state="normal")
                    import_btn.configure(state="normal")
                
                root.after(0, update_ui)
                
            except Exception as e:
                def show_error():
                    if not root.winfo_exists():
                        return  # 如果窗口已关闭，不执行操作
                    error_msg = str(e)
                    messagebox.showerror("错误", f"处理过程中出错: {error_msg}")
                    status_label.config(text="处理出错")
                    # 记录详细错误信息到日志
                    logger.error(f"处理出错: {error_msg}", exc_info=True)
                    # 恢复按钮状态
                    start_button.configure(state="normal")
                    import_btn.configure(state="normal")
                
                root.after(0, show_error)
        
        # 启动线程
        threading.Thread(target=processing_thread, daemon=True).start()
    
    # 设置窗口协议，确保窗口关闭时正确退出
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出程序吗?"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    return root


if __name__ == "__main__":
    try:
        # 如果有命令行参数，则使用命令行模式
        if len(sys.argv) > 1 and sys.argv[1] == "--console":
            print("以命令行模式运行...")
            main()
        else:
            # 否则使用GUI模式
            try:
                root = create_gui()
                
                # 启动GUI主循环
                root.mainloop()
            except Exception as gui_error:
                print(f"GUI启动失败: {str(gui_error)}")
                logger.error("GUI启动失败", exc_info=True)
                
                # 如果GUI模式失败，尝试回退到控制台模式
                print("尝试以命令行模式运行...")
                main()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        logger.error("程序启动失败", exc_info=True)
        
        # 显示错误对话框（如果可能）
        try:
            from tkinter import messagebox
            messagebox.showerror("严重错误", f"程序启动失败: {str(e)}")
        except:
            pass
            
        # 保持控制台窗口打开，以便用户看到错误信息
        input("按回车键退出...")