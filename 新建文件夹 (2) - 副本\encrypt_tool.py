import base64
import os
import random
import string
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('encrypt_tool.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("EncryptTool")

# 固定密钥，确保加密和解密使用相同的密钥
DEFAULT_KEY = "WalmartLicenseSecretKey2025"

def generate_key(length=24):
    """生成随机密钥"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def xor_encrypt(data, key):
    """使用XOR对数据进行加密/解密"""
    key_bytes = key.encode() if isinstance(key, str) else key
    data_bytes = data.encode() if isinstance(data, str) else data
    
    # 循环使用密钥的每个字节与数据进行异或操作
    key_len = len(key_bytes)
    encrypted = bytearray(len(data_bytes))
    
    for i in range(len(data_bytes)):
        encrypted[i] = data_bytes[i] ^ key_bytes[i % key_len]
    
    return bytes(encrypted)

def encrypt_file(file_path, output_path=None, key=DEFAULT_KEY):
    """
    加密文件内容
    
    参数:
        file_path: 要加密的文件路径
        output_path: 加密后的输出文件路径，如果为None则自动生成
        key: 加密密钥
    
    返回:
        加密后的文件路径
    """
    try:
        # 如果未指定输出路径，则生成默认路径
        if output_path is None:
            output_path = file_path + ".encrypted"
        
        logger.info(f"开始加密文件: {file_path}")
        
        # 读取文件内容
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # 加密内容
        encrypted_content = xor_encrypt(content, key)
        
        # Base64编码
        base64_content = base64.b64encode(encrypted_content)
        
        # 写入加密文件
        with open(output_path, 'wb') as f:
            f.write(base64_content)
        
        logger.info(f"文件加密成功，输出到: {output_path}")
        logger.info(f"原始文件大小: {len(content)} 字节，加密后大小: {len(base64_content)} 字节")
        
        return output_path
    
    except Exception as e:
        logger.error(f"加密文件时出错: {str(e)}")
        raise

def decrypt_content(encrypted_content, key=DEFAULT_KEY):
    """
    解密内容
    
    参数:
        encrypted_content: 加密后的内容 (base64编码的字节)
        key: 解密密钥
    
    返回:
        解密后的原始内容
    """
    try:
        # Base64解码
        decoded_content = base64.b64decode(encrypted_content)
        
        # 使用XOR解密
        decrypted_content = xor_encrypt(decoded_content, key)
        
        return decrypted_content
    
    except Exception as e:
        logger.error(f"解密内容时出错: {str(e)}")
        raise

def decrypt_file(file_path, output_path=None, key=DEFAULT_KEY):
    """
    解密文件
    
    参数:
        file_path: 加密文件的路径
        output_path: 解密后的输出文件路径，如果为None则自动生成
        key: 解密密钥
    
    返回:
        解密后的文件路径
    """
    try:
        # 如果未指定输出路径，则生成默认路径
        if output_path is None:
            if file_path.endswith(".encrypted"):
                output_path = file_path[:-10] + ".decrypted"
            else:
                output_path = file_path + ".decrypted"
        
        logger.info(f"开始解密文件: {file_path}")
        
        # 读取加密文件内容
        with open(file_path, 'rb') as f:
            encrypted_content = f.read()
        
        # 解密内容
        decrypted_content = decrypt_content(encrypted_content, key)
        
        # 写入解密文件
        with open(output_path, 'wb') as f:
            f.write(decrypted_content)
        
        logger.info(f"文件解密成功，输出到: {output_path}")
        logger.info(f"加密文件大小: {len(encrypted_content)} 字节，解密后大小: {len(decrypted_content)} 字节")
        
        return output_path
    
    except Exception as e:
        logger.error(f"解密文件时出错: {str(e)}")
        raise

def encrypt_files():
    """加密walmart_license_client.py和walmart_scraper.py文件"""
    try:
        logger.info("开始加密项目文件")
        
        # 确保使用相同的密钥
        key = DEFAULT_KEY
        logger.info(f"使用密钥: {key}")
        
        # 加密客户端文件
        client_file = "walmart_license_client.py"
        client_encrypted = encrypt_file(client_file, client_file + ".encrypted", key)
        logger.info(f"客户端文件加密完成: {client_encrypted}")
        
        # 加密采集程序文件
        scraper_file = "walmart_scraper.py"
        scraper_encrypted = encrypt_file(scraper_file, scraper_file + ".encrypted", key)
        logger.info(f"采集程序文件加密完成: {scraper_encrypted}")
        
        logger.info("所有文件加密完成")
        return True
    
    except Exception as e:
        logger.error(f"加密文件过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    encrypt_files() 