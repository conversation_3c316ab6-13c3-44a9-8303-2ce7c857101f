import os
import sys
import subprocess
import logging
from pathlib import Path
import shutil
import argparse

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_all.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("BuildAll")

# 定义文件路径
CLIENT_PY = "walmart_license_client.py"
SCRAPER_PY = "walmart_scraper.py"
LOADER_PY = "walmart_client_loader.py"
ICON_FILE = "icon.ico"
SERVER_PY = "walmart_license_server.py"
ENCRYPT_TOOL = "encrypt_tool.py"
BUILD_SCRIPT = "build_encrypted.py"

def check_prerequisites():
    """检查必要前提条件"""
    logger.info("检查前提条件...")
    
    # 检查必要文件是否存在
    required_files = [CLIENT_PY, SCRAPER_PY, LOADER_PY, ICON_FILE, 
                      SERVER_PY, ENCRYPT_TOOL, BUILD_SCRIPT]
    
    for file in required_files:
        if not os.path.exists(file):
            logger.error(f"缺少必要文件: {file}")
            return False
    
    # 检查PyInstaller是否已安装
    try:
        import PyInstaller
        logger.info(f"检测到PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        logger.error("未安装PyInstaller，请先安装: pip install pyinstaller")
        return False
    
    # 检查requests库是否已安装
    try:
        import requests
        logger.info(f"检测到requests版本: {requests.__version__}")
    except ImportError:
        logger.error("未安装requests库，请先安装: pip install requests")
        return False
    
    logger.info("所有前提条件检查通过")
    return True

def run_command(command, description):
    """执行命令并记录结果"""
    logger.info(f"执行: {description}")
    logger.info(f"命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, 
                               capture_output=True, text=True)
        logger.info(f"{description}执行成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"{description}执行失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"{description}过程出错: {str(e)}")
        return False

def encrypt_files():
    """加密文件"""
    logger.info("开始加密文件...")
    
    return run_command([sys.executable, ENCRYPT_TOOL], "加密文件")

def build_executable():
    """构建可执行文件"""
    logger.info("开始构建可执行文件...")
    
    return run_command([sys.executable, BUILD_SCRIPT], "构建可执行文件")

def check_server_config():
    """检查服务器配置，并提示用户需要做的修改"""
    logger.info("检查服务器配置...")
    
    try:
        # 检查服务器文件是否存在指定路径常量
        found_program_path = False
        found_encrypted_path = False
        
        with open(SERVER_PY, 'r', encoding='utf-8') as f:
            content = f.read()
            
            if "PROGRAM_PATH = " in content:
                found_program_path = True
            
            if ".encrypted" in content and "PROGRAM_PATH" in content:
                found_encrypted_path = True
        
        if not found_program_path:
            logger.warning("在服务器代码中未找到PROGRAM_PATH常量定义")
        
        if not found_encrypted_path:
            logger.warning("服务器代码未配置使用加密的程序文件路径(.encrypted)")
            logger.warning("请确保服务器端PROGRAM_PATH指向加密文件: walmart_scraper.py.encrypted")
        
        # 创建服务器文件目录
        server_dir = Path("server_files")
        server_dir.mkdir(exist_ok=True)
        
        # 复制需要上传到服务器的文件
        encrypted_scraper = f"{SCRAPER_PY}.encrypted"
        server_file = server_dir / encrypted_scraper
        
        if os.path.exists(encrypted_scraper):
            shutil.copy2(encrypted_scraper, server_file)
            logger.info(f"已复制加密文件到服务器目录: {server_file}")
        else:
            logger.error(f"找不到加密后的采集程序文件: {encrypted_scraper}")
            return False
        
        # 复制修改后的服务器文件
        server_file = server_dir / SERVER_PY
        shutil.copy2(SERVER_PY, server_file)
        logger.info(f"已复制服务器文件到: {server_file}")
        
        logger.info("服务器文件准备完成，请上传server_files目录中的文件到服务器")
        return True
        
    except Exception as e:
        logger.error(f"检查服务器配置时出错: {str(e)}")
        return False

def cleanup():
    """清理临时文件"""
    logger.info("清理临时文件...")
    
    try:
        # 清理PyInstaller生成的临时目录
        for dir_name in ['build', '__pycache__']:
            if os.path.exists(dir_name) and os.path.isdir(dir_name):
                shutil.rmtree(dir_name)
                logger.info(f"已删除目录: {dir_name}")
        
        # 清理spec文件
        for spec_file in Path('.').glob('*.spec'):
            os.remove(spec_file)
            logger.info(f"已删除spec文件: {spec_file}")
        
        return True
    except Exception as e:
        logger.error(f"清理临时文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="构建加密的沃尔玛客户端程序")
    parser.add_argument('--skip-checks', action='store_true', 
                       help='跳过前提条件检查')
    parser.add_argument('--skip-encryption', action='store_true',
                       help='跳过加密步骤')
    parser.add_argument('--skip-build', action='store_true',
                       help='跳过构建可执行文件步骤')
    parser.add_argument('--skip-server', action='store_true',
                       help='跳过服务器配置检查步骤')
    parser.add_argument('--skip-cleanup', action='store_true',
                       help='跳过清理临时文件步骤')
    
    args = parser.parse_args()
    
    logger.info("开始构建加密的沃尔玛客户端程序...")
    
    # 1. 检查前提条件
    if not args.skip_checks:
        logger.info("步骤1: 检查前提条件")
        if not check_prerequisites():
            logger.error("前提条件检查失败，终止构建")
            return False
    else:
        logger.info("跳过前提条件检查")
    
    # 2. 加密文件
    if not args.skip_encryption:
        logger.info("步骤2: 加密文件")
        if not encrypt_files():
            logger.error("加密文件失败，终止构建")
            return False
    else:
        logger.info("跳过加密文件步骤")
    
    # 3. 构建可执行文件
    if not args.skip_build:
        logger.info("步骤3: 构建可执行文件")
        if not build_executable():
            logger.error("构建可执行文件失败，终止构建")
            return False
    else:
        logger.info("跳过构建可执行文件步骤")
    
    # 4. 检查服务器配置
    if not args.skip_server:
        logger.info("步骤4: 检查服务器配置")
        if not check_server_config():
            logger.error("检查服务器配置失败")
            # 继续执行，因为这不是致命错误
    else:
        logger.info("跳过服务器配置检查步骤")
    
    # 5. 清理临时文件
    if not args.skip_cleanup:
        logger.info("步骤5: 清理临时文件")
        if not cleanup():
            logger.warning("清理临时文件失败")
            # 继续执行，因为这不是致命错误
    else:
        logger.info("跳过清理临时文件步骤")
    
    logger.info("构建过程完成")
    logger.info("请将dist目录中的可执行文件分发给用户")
    logger.info("请将server_files目录中的文件上传到服务器")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 