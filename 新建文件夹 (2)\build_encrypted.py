import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
from encrypt_tool import encrypt_file, decrypt_file

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("BuildScript")

# 定义文件路径
CLIENT_PY = "walmart_license_client.py"
SCRAPER_PY = "walmart_scraper.py"
LOADER_PY = "walmart_client_loader.py"
ICON_FILE = "icon.ico"

# 加密密钥
ENCRYPT_KEY = "WalmartLicenseSecretKey2025"

def encrypt_files():
    """加密客户端和采集程序文件"""
    logger.info("开始加密文件...")
    
    # 加密客户端文件
    client_encrypted = encrypt_file(CLIENT_PY, CLIENT_PY + ".encrypted", ENCRYPT_KEY)
    logger.info(f"客户端文件加密完成: {client_encrypted}")
    
    # 加密采集程序文件
    scraper_encrypted = encrypt_file(SCRAPER_PY, SCRAPER_PY + ".encrypted", ENCRYPT_KEY)
    logger.info(f"采集程序文件加密完成: {scraper_encrypted}")
    
    return client_encrypted, scraper_encrypted

def build_executable():
    """使用PyInstaller构建可执行文件"""
    logger.info("开始构建可执行文件...")
    
    # 确保PyInstaller已安装
    try:
        import PyInstaller
        logger.info(f"检测到PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        logger.error("未安装PyInstaller，请先安装: pip install pyinstaller")
        return False
    
    # 准备PyInstaller命令
    pyinstaller_cmd = [
        "pyinstaller",
        "--onefile",                      # 单文件模式
        "--windowed",                     # 无控制台窗口
        "--clean",                        # 清理临时文件
        f"--icon={ICON_FILE}",            # 设置图标
        "--name=沃尔玛产品授权客户端",       # 输出文件名
        "--add-data", f"{CLIENT_PY}.encrypted;.",  # 添加加密文件
        "--add-data", f"{ICON_FILE};.",    # 添加图标文件
        LOADER_PY                          # 主程序
    ]
    
    logger.info(f"执行命令: {' '.join(pyinstaller_cmd)}")
    
    # 执行PyInstaller
    try:
        result = subprocess.run(pyinstaller_cmd, check=True, capture_output=True, text=True)
        logger.info("PyInstaller构建成功")
        logger.debug(f"输出: {result.stdout}")
        
        # 检查dist目录
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))
        
        if exe_files:
            logger.info(f"生成的可执行文件: {[str(f) for f in exe_files]}")
            return True
        else:
            logger.error("构建成功但未找到exe文件")
            return False
        
    except subprocess.CalledProcessError as e:
        logger.error(f"PyInstaller构建失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"构建过程出错: {str(e)}")
        return False

def test_decrypt():
    """测试解密功能正常工作"""
    logger.info("测试解密功能...")
    
    test_file = CLIENT_PY + ".encrypted"
    try:
        decrypted_file = decrypt_file(test_file, CLIENT_PY + ".test_decrypted", ENCRYPT_KEY)
        logger.info(f"测试解密成功: {decrypted_file}")
        
        # 比较文件内容
        with open(CLIENT_PY, 'rb') as f1, open(decrypted_file, 'rb') as f2:
            original = f1.read()
            decrypted = f2.read()
            
        if original == decrypted:
            logger.info("解密内容与原始内容完全一致")
            return True
        else:
            logger.error("解密内容与原始内容不一致")
            return False
            
    except Exception as e:
        logger.error(f"测试解密失败: {str(e)}")
        return False
    finally:
        # 清理测试文件
        test_decrypted = CLIENT_PY + ".test_decrypted"
        if os.path.exists(test_decrypted):
            os.remove(test_decrypted)

def prepare_for_server():
    """准备服务器端文件"""
    logger.info("准备服务器端文件...")
    
    try:
        # 创建服务器文件目录
        server_dir = Path("server_files")
        server_dir.mkdir(exist_ok=True)
        
        # 复制加密后的采集程序文件到服务器目录
        encrypted_scraper = SCRAPER_PY + ".encrypted"
        server_file = server_dir / encrypted_scraper
        shutil.copy2(encrypted_scraper, server_file)
        
        logger.info(f"已复制加密文件到服务器目录: {server_file}")
        return True
        
    except Exception as e:
        logger.error(f"准备服务器文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始构建加密的沃尔玛客户端程序...")
    
    # 1. 加密文件
    logger.info("步骤1: 加密文件")
    client_encrypted, scraper_encrypted = encrypt_files()
    
    # 2. 测试解密
    logger.info("步骤2: 测试解密功能")
    if not test_decrypt():
        logger.error("解密测试失败，终止构建")
        return False
    
    # 3. 构建可执行文件
    logger.info("步骤3: 构建可执行文件")
    if not build_executable():
        logger.error("构建可执行文件失败，终止构建")
        return False
    
    # 4. 准备服务器文件
    logger.info("步骤4: 准备服务器文件")
    if not prepare_for_server():
        logger.error("准备服务器文件失败")
        # 继续执行，因为这不是致命错误
    
    logger.info("构建过程完成")
    logger.info("请将dist目录中的可执行文件分发给用户")
    logger.info("请将server_files目录中的文件上传到服务器")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 