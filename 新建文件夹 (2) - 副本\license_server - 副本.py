from flask import Flask, request, jsonify, send_file
from datetime import datetime, timedelta
import json
import os
import hashlib
import time
from flask_cors import CORS
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/license_server.log'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 存储激活码信息的数据库文件
LICENSE_DB = "/opt/license_manager/license_database.json"
# 程序文件存放位置
PROGRAM_PATHS = {
    "价格": "/opt/license_manager/历史价格7.py.encrypted",
    "历史价格": "/opt/license_manager/历史价格7.py.encrypted",  # 添加别名支持
    "采集": "/opt/license_manager/采集8.py.encrypted",
    "筛品": "/opt/license_manager/筛品终极版1.py.encrypted"
}
# 图标文件存放位置
ICON_PATH = "/opt/license_manager/icon.ico"
# 欧陆扩展路径 - 修改为指向zip压缩文件
EXTENSION_PATH = "/opt/license_manager/oalur-extension-V1.9.3_3.zip"

# 权限级别定义
PERMISSION_LEVELS = {
    1: ["采集"],
    2: ["采集", "筛品"],
    3: ["采集", "筛品", "价格", "历史价格"]  # 添加"历史价格"别名支持
}

# 初始化数据库
def init_database():
    if not os.path.exists(LICENSE_DB):
        with open(LICENSE_DB, 'w') as f:
            json.dump({}, f)
        logging.info("Created new license database")
        
    # 确保图标文件存在
    if not os.path.exists(ICON_PATH):
        logging.warning(f"Icon file not found at {ICON_PATH}")

# 加载数据库
def load_database():
    try:
        with open(LICENSE_DB, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Error loading database: {str(e)}")
        return {}

# 保存数据库
def save_database(data):
    try:
        with open(LICENSE_DB, 'w') as f:
            json.dump(data, f, indent=4)
    except Exception as e:
        logging.error(f"Error saving database: {str(e)}")

# 生成激活码
def generate_key(expire_days=30, permission_level=1):
    import random
    import string
    
    # 生成随机ID
    random_id = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
    
    # 计算过期日期
    expire_date = (datetime.now() + timedelta(days=expire_days)).strftime("%Y%m%d")
    
    # 生成验证码
    secret_key = "T0P$ECRET-K3Y-F0R-V3RIF1C4TI0N"
    verify_code = hashlib.sha256((random_id + expire_date + secret_key).encode()).hexdigest()[:8]
    
    # 组合成完整的激活码
    key = f"{random_id}-{expire_date}-{verify_code}"
    return key

# 签名验证函数
def check_signature(data):
    """验证请求签名"""
    # 验证所需字段
    required_fields = ["key", "device_id", "timestamp", "nonce", "signature"]
    for field in required_fields:
        if field not in data:
            return False, f"缺少必要字段: {field}"
    
    # 验证时间戳（防止重放攻击）
    try:
        timestamp = int(data["timestamp"])
        current_time = int(time.time())
        # 允许5分钟时间差
        if abs(current_time - timestamp) > 300:
            return False, "请求已过期，请检查系统时间"
    except:
        return False, "无效的时间戳"
    
    # 构建签名数据
    key = data["key"]
    device_id = data["device_id"]
    timestamp = data["timestamp"]
    nonce = data["nonce"]
    signature = data["signature"]
    
    # 计算预期签名
    signature_data = f"{key}:{device_id}:{timestamp}:{nonce}"
    expected_signature = hashlib.sha256(signature_data.encode()).hexdigest()
    
    # 验证签名
    if signature != expected_signature:
        return False, "请求签名无效"
    
    return True, ""

# 验证激活码
@app.route('/license/check', methods=['POST'])
def check_license():
    try:
        data = request.json
        
        # 验证请求签名
        if "signature" in data:
            valid_sig, error_msg = check_signature(data)
            if not valid_sig:
                logging.warning(f"签名验证失败: {error_msg}, 来自设备: {data.get('device_id', 'unknown')}")
                return jsonify({
                    "valid": False,
                    "message": error_msg
                })
        
        key = data.get('key')
        device_id = data.get('device_id')
        
        if not key or not device_id:
            return jsonify({
                "valid": False,
                "message": "缺少必要参数"
            })
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "valid": False,
                "message": "激活码不存在"
            })
        
        license_info = db[key]
        
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return jsonify({
                "valid": False,
                "message": "激活码已过期"
            })
        
        # 检查设备绑定
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return jsonify({
                "valid": False,
                "message": "激活码已绑定到其他设备"
            })
        
        # 如果未绑定设备，则绑定当前设备
        if not license_info.get('device_id'):
            license_info['device_id'] = device_id
            license_info['activated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            db[key] = license_info
            save_database(db)
        
        # 获取权限级别，默认为1
        permission_level = license_info.get('permission_level', 1)
        
        # 获取该权限级别允许的程序列表
        allowed_programs = PERMISSION_LEVELS.get(permission_level, ["采集"])
        
        # 生成服务器响应签名
        timestamp = data.get("timestamp", "")
        nonce = data.get("nonce", "")
        server_data = f"True:{key}:{timestamp}:{nonce}"
        server_signature = hashlib.sha256(server_data.encode()).hexdigest()
        
        return jsonify({
            "valid": True,
            "message": "验证成功",
            "expire_date": license_info['expire_date'],
            "permission_level": permission_level,
            "allowed_programs": allowed_programs,
            "server_signature": server_signature
        })
        
    except Exception as e:
        logging.error(f"Error in check_license: {str(e)}")
        return jsonify({
            "valid": False,
            "message": f"验证出错: {str(e)}"
        })

# 生成新的激活码
@app.route('/license/generate', methods=['POST'])
def generate_license():
    try:
        data = request.json
        expire_days = data.get('expire_days', 30)
        quantity = data.get('quantity', 1)
        permission_level = data.get('permission_level', 1)  # 添加权限级别参数，默认为1
        
        # 验证权限级别是否有效（1-3）
        if permission_level not in [1, 2, 3]:
            return jsonify({
                "success": False,
                "message": "无效的权限级别，必须是1-3之间的整数"
            })
            
        keys = []
        db = load_database()
        
        for _ in range(quantity):
            key = generate_key(expire_days)
            db[key] = {
                "expire_date": key.split('-')[1],
                "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "未使用",
                "permission_level": permission_level  # 保存权限级别
            }
            keys.append(key)
        
        save_database(db)
        
        return jsonify({
            "success": True,
            "keys": keys
        })
        
    except Exception as e:
        logging.error(f"Error in generate_license: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"生成激活码出错: {str(e)}"
        })

# 修改激活码状态
@app.route('/license/modify', methods=['POST'])
def modify_license():
    try:
        data = request.json
        key = data.get('key')
        action = data.get('action')  # 'extend', 'revoke', 'reset', 'change_level'
        days = data.get('days', 0)
        permission_level = data.get('permission_level', None)  # 用于修改权限级别
        
        if not key or not action:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            })
        
        db = load_database()
        
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            })
        
        license_info = db[key]
        
        if action == 'extend':
            # 延长有效期
            current_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
            new_date = current_date + timedelta(days=days)
            license_info['expire_date'] = new_date.strftime("%Y%m%d")
            license_info['status'] = "已延期"
            logging.info(f"License {key} extended by {days} days. New expiry: {license_info['expire_date']}")
            
        elif action == 'revoke':
            # 使激活码失效
            yesterday = datetime.now() - timedelta(days=1)
            license_info['expire_date'] = yesterday.strftime("%Y%m%d")
            license_info['status'] = "已撤销"
            logging.info(f"License {key} revoked. Set to expire on: {license_info['expire_date']}")
            
        elif action == 'reset':
            # 重置设备绑定并扣除1天有效期
            # 移除设备绑定信息
            license_info.pop('device_id', None)
            license_info.pop('activated_at', None)
            
            # 计算新的到期日期（减去1天）
            current_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
            new_date = current_date - timedelta(days=1)
            
            # 如果扣除后日期小于当前日期，则设置为当前日期（避免立即过期）
            if new_date < datetime.now():
                new_date = datetime.now() + timedelta(hours=12)  # 至少保留12小时有效期
                
            license_info['expire_date'] = new_date.strftime("%Y%m%d")
            license_info['status'] = "已重置设备(扣1天)"
            license_info['last_reset'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            logging.info(f"License {key} reset for new device. Penalty: -1 day. New expiry: {license_info['expire_date']}")
            
        elif action == 'change_level':
            # 修改权限级别
            if permission_level is not None and permission_level in [1, 2, 3]:
                license_info['permission_level'] = permission_level
                license_info['status'] = "已修改权限"
                logging.info(f"License {key} permission level changed to {permission_level}")
            else:
                return jsonify({
                    "success": False,
                    "message": "无效的权限级别，必须是1-3之间的整数"
                })
        
        db[key] = license_info
        save_database(db)
        
        # 验证更改是否保存成功
        verify_db = load_database()
        if key in verify_db and verify_db[key] == license_info:
            return jsonify({
                "success": True,
                "message": "操作成功",
                "info": license_info
            })
        else:
            return jsonify({
                "success": False,
                "message": "操作未能保存到数据库"
            })
        
    except Exception as e:
        logging.error(f"Error in modify_license: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"操作出错: {str(e)}"
        })

# 查询激活码信息
@app.route('/license/info', methods=['GET'])
def get_license_info():
    try:
        key = request.args.get('key')
        
        if not key:
            return jsonify({
                "success": False,
                "message": "缺少激活码参数"
            })
        
        db = load_database()
        
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            })
        
        return jsonify({
            "success": True,
            "info": db[key]
        })
        
    except Exception as e:
        logging.error(f"Error in get_license_info: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"查询出错: {str(e)}"
        })

# 下载主程序
@app.route('/license/download', methods=['GET'])
def download_program():
    try:
        # 从URL参数中获取程序类型，支持三种参数名 'type'、'program_type' 和 'program'
        program_type = request.args.get('program_type') or request.args.get('type') or request.args.get('program', '价格')
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        # 添加更详细的日志，列出所有请求参数
        logging.info(f"下载程序请求: 所有参数={request.args}")
        logging.info(f"下载程序请求: program_type={program_type}, key={key}")
        
        if not key or not device_id:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            }), 400
            
        # 验证激活码
        db = load_database()
        
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            }), 403
            
        license_info = db[key]
        
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return jsonify({
                "success": False,
                "message": "激活码已过期"
            }), 403
            
        # 检查设备绑定
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return jsonify({
                "success": False,
                "message": "激活码已绑定到其他设备"
            }), 403
            
        # 获取权限级别并检查程序访问权限
        permission_level = license_info.get('permission_level', 1)
        allowed_programs = PERMISSION_LEVELS.get(permission_level, ["采集"])
        
        logging.info(f"用户请求程序: {program_type}, 权限级别: {permission_level}, 允许的程序: {allowed_programs}")
        
        # 特殊处理"历史价格"和"价格"的兼容
        check_program_type = program_type
        if program_type == "历史价格" and "价格" in allowed_programs:
            check_program_type = "价格"  # 如果允许"价格"，则"历史价格"也被允许
        
        if check_program_type not in allowed_programs:
            logging.warning(f"用户 {device_id} 尝试下载未授权程序: {program_type}, 权限级别: {permission_level}")
            return jsonify({
                "success": False,
                "message": f"您的许可证级别({permission_level})不允许使用{program_type}程序"
            }), 403
        
        if program_type not in PROGRAM_PATHS:
            return jsonify({
                "success": False,
                "message": "无效的程序类型"
            }), 400

        program_path = PROGRAM_PATHS[program_type]
        
        # 添加文件路径和大小信息到日志
        logging.info(f"准备发送文件: {program_path}")
        logging.info(f"文件大小: {os.path.getsize(program_path) if os.path.exists(program_path) else '文件不存在'} 字节")
        
        # 检查文件是否存在
        if not os.path.exists(program_path):
            return jsonify({
                "success": False,
                "message": "程序文件不存在"
            }), 404
            
        # 记录下载日志
        logging.info(f"用户 {device_id} 下载了 {program_type} 程序")

        # 发送文件
        return send_file(
            program_path,
            as_attachment=True,
            download_name=os.path.basename(program_path)
        )
    except Exception as e:
        logging.error(f"Error downloading program: {str(e)}")
        return jsonify({
            "success": False,
            "message": "下载失败"
        }), 500

# 下载图标文件
@app.route('/license/download_icon', methods=['GET'])
def download_icon():
    try:
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        if not key or not device_id:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            })
        
        # 验证激活码
        db = load_database()
        
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            })
        
        license_info = db[key]
        
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return jsonify({
                "success": False,
                "message": "激活码已过期"
            })
        
        # 检查设备绑定
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return jsonify({
                "success": False,
                "message": "激活码已绑定到其他设备"
            })
            
        # 检查图标文件是否存在
        if not os.path.exists(ICON_PATH):
            return jsonify({
                "success": False,
                "message": "图标文件不存在"
            })
            
        # 记录下载日志
        logging.info(f"Icon downloaded by key:{key}, device:{device_id}")
        
        # 发送文件
        return send_file(
            ICON_PATH,
            as_attachment=True,
            download_name="icon.ico",
            mimetype="image/x-icon"
        )
        
    except Exception as e:
        logging.error(f"Error in download_icon: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"下载图标出错: {str(e)}"
        })

# 下载扩展
@app.route('/extension/download', methods=['GET'])
def download_extension():
    try:
        # 获取参数
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        if not key or not device_id:
            return "缺少必要参数", 400
            
        # 验证激活码
        db = load_database()
        if key not in db:
            return "无效的激活码", 403
            
        license_info = db[key]
        
        # 检查是否已绑定设备
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return "激活码已绑定到其他设备", 403
            
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return "激活码已过期", 403
        
        # 检查扩展文件是否存在
        if not os.path.exists(EXTENSION_PATH):
            return "扩展不存在", 404
            
        # 记录下载日志
        logging.info(f"用户 {device_id} 下载了欧陆扩展")
        
        # 直接发送zip文件
        return send_file(
            EXTENSION_PATH,
            mimetype='application/zip',
            as_attachment=True,
            download_name='oalur-extension-V1.9.3_3.zip'
        )
        
    except Exception as e:
        logging.error(f"扩展下载出错: {str(e)}")
        return f"下载出错: {str(e)}", 500

# 主程序入口点
if __name__ == "__main__":
    # 确保数据库文件存在
    init_database()
    # 在生产环境中应该使用 gunicorn 或 uwsgi
    app.run(host="0.0.0.0", port=44285) 