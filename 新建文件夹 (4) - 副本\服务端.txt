mkdir -p /opt/license_manager
touch /opt/license_manager/license_database.json
chmod 755 /opt/license_manager
chmod 644 /opt/license_manager/license_database.json
touch /var/log/license_server.log 
chmod 644 /var/log/license_server.log 
chmod +x /root/license_server.py
pip install flask flask-cors
python3 /root/license_server.py
nohup python3 /root/license_server.py > /root/license_server.log 2>&1 &

touch /opt/license_manager/walmart_license_database.json
chmod 755 /opt/license_manager
chmod 644 /opt/license_manager/walmart_license_database.json
touch /var/log/walmart_license_server.log 
chmod 644 /var/log/walmart_license_server.log 
chmod +x /root/walmart_license_server.py
nohup python3 /root/walmart_license_server.py > /root/walmart_license_server.log 2>&1 &
 ./deploy_license_server.sh

查看当前文件夹，帮我分析，我需要把代码上传到服务端，每次运行客户端通过验证就会从服务端下载这个代码，历史价格7.py是主程序，保留验证方法生成另外一个代码，license_server.py这个是响应端，license_manager.py这个是验证码生成器，我想直接上传历史价格7.py到服务器，到时候验证过完直接下载然后执行历史价格7.py就行，帮我修改给我中文解释

分析当前文件夹的所有代码，我想打包当前license_client.py代码，其他三个都是从服务器下载下来的，这三个不需要打包进exe，只需要后面下载下来执行就行，打包exe必须有这个三个文件对应得依赖，然后我打包的license_client.py的exx也能执行这3个python文件，也就是说exe包含它们的所有依赖,图标也在当前文件夹C:\Users\<USER>\Desktop\fsdownload\1234\icon.ico，容易出错的依赖可以不用打包，用自动更新加载的办法也可以，运行的时候检查是否有这些依赖和python，如果没有就自动下载，不要出现运行程序出现没有对应的库的情况，给我完美的打包方案，我的都是微软驱动，后面运行软件的时候先判断是否有对应版本驱动，如果没有那就自动更新驱动，需要根据3个软件使用的驱动匹配，而不是直接使用谷歌驱动，也不需要Firefox这个驱动，只检查微软和谷歌驱动就行，给我中文回复

分析当前文件夹的所有代码，我想加密并打包当前license_client.py代码，其他三个采集8.py，筛品终极版1.py，历史价格7.py都是从服务器下载下来的，这三个不需要打包成exe，但是要加密，只需要后面下载下来执行就行，然后我打包的license_client.py的exe需要检查那3个代码的依赖和python解释器，如果没有就安装依赖,图标也在当前文件夹C:\Users\<USER>\Desktop\fsdownload\1234\icon.ico，采集8.py，筛品终极版1.py，历史价格7.py这3个的界面的主题放到它们的代码里面，而不是依靠ui_theme.py主题代码，还需要修改license_server.py这个文件，要考虑到下载代码的名称，给我能够直接运行的修改好的代码，写好加密打包文件直接运行就行，参考build_all.py加密打包代码

我仔细分析了license_client.py代码，找到了问题所在：
主要原因是Python解释器路径错误。在SimpleClient.run_program方法中：
Apply to license_clie...
当程序被打包成EXE后，sys.executable返回的不是Python解释器路径，而是EXE文件自身的路径。这导致构建的命令实际上变成了：
Apply to license_clie...
所以每次点击按钮，它都是在尝试用客户端程序（而不是Python解释器）去执行下载的Python脚本，结果只是启动了新的客户端界面。
解决方案需要修改代码，使用正确的Python解释器路径来执行下载的程序，而不是使用sys.executable。


我希望把采集，历史价格，筛品的结果都统一保存到当前主程序license_client.py所在的文件夹，而不是临时文件夹，这个能办到吗


帮我分析一下当前文件夹的历史价格7.py和采集8.py和筛品终极版1.py这3个驱动有什么不一样，我觉得历史价格7最好，采集要每次启动都需要下载驱动