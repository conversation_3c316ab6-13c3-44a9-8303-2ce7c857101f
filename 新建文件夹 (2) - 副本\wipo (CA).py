import pandas as pd
from bs4 import BeautifulSoup
import re
import unicodedata
import time
import random
import urllib.parse
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
from fake_useragent import UserAgent
import logging
import os
import concurrent.futures
import threading
import math
import sys
import hashlib
from selenium.webdriver.common.keys import Keys

# Setup logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加一个全局锁，用于同步文件的写入
file_lock = threading.Lock()

def md5_encrypt(string):
    """MD5加密字符串"""
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def get_headers():
    """生成随机请求头，参考1.py实现"""
    # 版本号
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'Accept': "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}"
    }
    return headers

def normalize_brand(brand):
    """Normalize brand name for comparison"""
    normalized = unicodedata.normalize('NFKD', brand)
    normalized = re.sub(r'[\u0300-\u036f]', '', normalized)
    normalized = re.sub(r'[^a-zA-Z]', '', normalized)
    return normalized.upper()

def get_random_ua():
    """Get a random user agent"""
    try:
        ua = UserAgent()
        return ua.random
    except:
        # 动态生成随机User-Agent字符串
        # 参考队列.py的实现方式，随机生成浏览器版本号和WebKit版本号
        def generate_dynamic_ua():
            # 随机版本号
            chrome_version = random.randint(100, 134)  # Chrome版本
            brand_version = random.randint(10, 25)     # Not A Brand版本
            webkit_version = random.randint(400, 600)  # WebKit版本
            
            # 操作系统列表
            os_list = [
                "Windows NT 10.0; Win64; x64",
                "Windows NT 11.0; Win64; x64",
                "Macintosh; Intel Mac OS X 10_15_7",
                "Macintosh; Intel Mac OS X 11_0_0",
                "X11; Linux x86_64",
                "X11; Ubuntu; Linux x86_64"
            ]
            
            # 浏览器类型
            browser_types = [
                # Chrome
                f"Mozilla/5.0 ({random.choice(os_list)}) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36",
                # Firefox
                f"Mozilla/5.0 ({random.choice(os_list)}; rv:{chrome_version}.0) Gecko/20100101 Firefox/{chrome_version}.0",
                # Edge
                f"Mozilla/5.0 ({random.choice(os_list)}) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36 Edg/{chrome_version}.0.{random.randint(1000, 2000)}.{random.randint(10, 99)}",
                # Brave
                f"Mozilla/5.0 ({random.choice(os_list)}) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36 Brave/{chrome_version}.0.0.0",
            ]
            
            return random.choice(browser_types)
        
        # 备用静态User-Agent列表，仅当动态生成失败时使用
        fallback_user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36'
        ]
        
        # 尝试动态生成User-Agent，失败时回退到静态列表
        try:
            return generate_dynamic_ua()
        except:
            return random.choice(fallback_user_agents)

def search_trademark(brand, driver, max_retries=3, is_first_search=False):
    """Search for trademark using Selenium browser with retry mechanism"""
    # 添加1-2秒的随机延迟
    delay_seconds = random.uniform(1, 2)
    logger.info(f"Adding delay of {delay_seconds:.2f} seconds before search...")
    time.sleep(delay_seconds)
    
    # 每次搜索前强制刷新页面，确保清除上一次搜索的状态
    logger.info("Refreshing page to clear previous search state...")
    try:
        driver.get("https://www.trademarkia.com/")
        # 等待页面完全加载
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        logger.info("Page refreshed successfully")
    except Exception as refresh_e:
        logger.warning(f"Error refreshing page: {str(refresh_e)}")
        # 如果直接导航失败，尝试刷新当前页面
        try:
            driver.refresh()
            time.sleep(2)  # 给页面刷新一些时间
            logger.info("Used refresh() as fallback")
        except Exception as e:
            logger.error(f"Even refresh failed: {str(e)}")
    
    retry_count = 0
    while retry_count < max_retries:
        try:
            # 等待页面加载
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 尝试查找搜索输入框，使用多种选择器
            logger.info(f"Looking for search input field to enter brand: {brand}")
            search_input = None
            try:
                # 尝试使用提供的选择器
                search_input = WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='search'][role='search']"))
                )
                logger.info("Found search input using CSS selector: input[type='search'][role='search']")
            except Exception as e:
                logger.warning(f"Failed to find search input with primary selector: {str(e)}")
                try:
                    # 备用选择器1：可能的class属性
                    search_input = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".flex-1.border-0"))
                    )
                    logger.info("Found search input using fallback CSS selector: .flex-1.border-0")
                except Exception as e2:
                    logger.warning(f"Failed to find search input with fallback selector: {str(e2)}")
                    try:
                        # 备用选择器2：任何可能的搜索输入框
                        search_input = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='Search']"))
                        )
                        logger.info("Found search input using generic search placeholder selector")
                    except Exception as e3:
                        logger.warning(f"Failed to find search input with generic selector: {str(e3)}")
                        # 尝试直接使用URL搜索
                        encoded_brand = urllib.parse.quote(brand)
                        logger.info(f"Falling back to direct URL navigation for search: {brand}")
                        search_url = f"https://www.trademarkia.com/search/trademarks?q={encoded_brand}&reset_page=true&country=ca"
                        driver.get(search_url)
                        logger.info(f"Navigated directly to search URL: {search_url}")
                        search_input = None  # 设为None，表示使用了直接URL访问
            
            # 如果找到搜索框，则使用它进行搜索
            if search_input is not None:
                logger.info(f"Clearing search field before entering new brand...")
                
                # 方法1: 使用标准clear方法
                try:
                    search_input.clear()
                    logger.info("Used standard clear() method")
                except Exception as clear_e:
                    logger.warning(f"Standard clear failed: {str(clear_e)}")
                
                # 方法2: 使用JavaScript直接清空值
                try:
                    driver.execute_script("arguments[0].value = '';", search_input)
                    logger.info("Used JavaScript to clear value")
                except Exception as js_e:
                    logger.warning(f"JavaScript clear failed: {str(js_e)}")
                
                # 方法3: 使用键盘组合Ctrl+A然后Delete
                try:
                    search_input.send_keys(Keys.CONTROL + "a")
                    search_input.send_keys(Keys.DELETE)
                    logger.info("Used Ctrl+A and Delete to clear")
                except Exception as keys_e:
                    logger.warning(f"Keyboard shortcut clear failed: {str(keys_e)}")
                
                # 方法4: 检查输入框是否为空，如果不为空则使用退格键逐字符删除
                try:
                    current_value = search_input.get_attribute('value')
                    if current_value:
                        logger.info(f"Input still has content: '{current_value}', using backspace method")
                        for _ in range(len(current_value)):
                            search_input.send_keys(Keys.BACKSPACE)
                        logger.info("Used backspace method to clear remaining text")
                except Exception as backspace_e:
                    logger.warning(f"Backspace clear failed: {str(backspace_e)}")
                
                # 再次检查输入框是否真的为空
                try:
                    final_value = search_input.get_attribute('value')
                    if final_value:
                        logger.warning(f"Warning: Input field still contains text after all clearing attempts: '{final_value}'")
                        # 最后的尝试：强制刷新页面再试一次
                        driver.refresh()
                        time.sleep(2)
                        # 重新查找搜索框
                        search_input = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='search'][role='search']"))
                        )
                        logger.info("Refreshed page as last resort to clear input field")
                    else:
                        logger.info("Confirmed: Input field is empty and ready for new search")
                except Exception as check_e:
                    logger.warning(f"Could not perform final empty check: {str(check_e)}")
                
                # 快速输入品牌名，不再逐字符输入
                search_input.send_keys(brand)
                logger.info(f"Entered brand name into search field: {brand}")
                
                # 等待一小段时间确保输入被接受
                time.sleep(0.5)
                
                # 检查输入是否正确
                try:
                    input_value = search_input.get_attribute('value')
                    if input_value != brand:
                        logger.warning(f"Input value mismatch! Expected: '{brand}', Actual: '{input_value}'")
                        # 重新输入
                        search_input.clear()
                        search_input.send_keys(brand)
                        logger.info("Re-entered brand name")
                except Exception as value_e:
                    logger.warning(f"Could not verify input value: {str(value_e)}")
                
                # 尝试查找并点击搜索按钮
                logger.info("Looking for search button to click")
                search_button = None
                try:
                    # 使用XPath直接定位包含"Search"文本的按钮内部元素
                    random_wait = random.uniform(2, 5)  # 随机等待2-5秒
                    logger.info(f"Waiting {random_wait:.2f} seconds for search button to be clickable")
                    
                    search_button = WebDriverWait(driver, random_wait).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[.//text()[contains(., 'Search')]]"))
                    )
                    logger.info("Found search button using XPath with nested 'Search' text")
                except Exception as e2:
                    logger.warning(f"Failed to find search button with XPath: {str(e2)}")
                    try:
                        # 回退到使用submit()方法
                        logger.info("Trying to submit the form directly")
                        search_input.submit()
                        logger.info("Used form submit() method to submit search")
                        search_button = None  # 设为None，表示使用了submit方法
                    except Exception as e4:
                        logger.error(f"Failed to submit search form: {str(e4)}")
                        # 最后尝试使用Enter键
                        search_input.send_keys(Keys.RETURN)
                        logger.info("Used RETURN key to submit search")
                        search_button = None  # 设为None，表示使用了Enter键
                
                # 如果找到搜索按钮，点击它
                if search_button is not None:
                    # 点击搜索按钮
                    logger.info("Clicking search button")
                    search_button.click()
            
            # 等待搜索结果加载
            logger.info("Waiting for search results to load...")
            
            # 然后等待搜索结果或"无结果"消息出现
            try:
                # 等待搜索结果或无结果消息出现
                WebDriverWait(driver, 20).until(
                    lambda d: len(d.find_elements(By.CSS_SELECTOR, 'a.table-row')) > 0 or 
                             len(d.find_elements(By.CSS_SELECTOR, '.tmTable.table-row')) > 0 or
                             len(d.find_elements(By.CSS_SELECTOR, 'h1.mr-auto')) > 0
                )
                
                # 检查页面是否加载成功或有效的"无结果"消息
                page_source = driver.page_source
                
                # 记录当前URL，便于调试
                logger.info(f"Current URL after search: {driver.current_url}")
                
                # 仅在找到特定错误消息且没有搜索结果时才认为是错误
                soup = BeautifulSoup(page_source, 'html.parser')
                
                # 如果有搜索结果，则不是错误
                if soup.select('a.table-row') or soup.select('.tmTable.table-row'):
                    logger.info("Search results found on page")
                    return page_source
                
                # 检查是否是有效的"无结果"页面
                count_heading = soup.find('h1', class_='mr-auto')
                if count_heading:
                    count_text = count_heading.get_text()
                    logger.info(f"Found count heading: '{count_text}'")
                    if 'found' in count_text.lower() or 'about 0' in count_text.lower():
                        logger.info(f"Valid page with no results: {count_text}")
                        return page_source
                
                # 否则检查错误
                if 'something went wrong' in page_source.lower() or 'server error' in page_source.lower():
                    logger.error("Error page detected in response")
                    raise Exception("Error page detected")
                
                # 如果来到这里，假设是有效的页面
                logger.info("No specific content detected, but assuming valid page")
                return page_source
                
            except Exception as inner_e:
                logger.warning(f"Wait for content failed: {str(inner_e)}")
                raise inner_e
            
        except Exception as e:
            retry_count += 1
            logger.warning(f"Attempt {retry_count}/{max_retries} failed: {str(e)}")
            if retry_count < max_retries:
                wait_time = 1 + random.random()  # 1-2秒的随机延迟
                logger.info(f"Retrying in {wait_time:.2f} seconds...")
                time.sleep(wait_time)
                
                # 尝试刷新页面，增加重试成功率
                try:
                    driver.refresh()
                except:
                    pass
                
                # 如果多次失败，尝试直接使用URL搜索
                if retry_count == max_retries - 1:
                    logger.info("Last retry attempt, trying direct URL navigation")
                    encoded_brand = urllib.parse.quote(brand)
                    search_url = f"https://www.trademarkia.com/search/trademarks?q={encoded_brand}&reset_page=true&country=ca"
                    logger.info(f"Navigating directly to: {search_url}")
                    driver.get(search_url)
            else:
                logger.error(f"Failed to search after {max_retries} attempts: {str(e)}")
                return None

def extract_trademark_data(html_content, brand_name):
    """Extract trademark data from HTML content"""
    if not html_content:
        return {'b': 0, 'c1': "", 'c2': "", 'c3': "", 'SL': 0}
    
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Only check for error page if there are no table rows
        table_rows = soup.select('a.table-row.tmTable') or soup.select('a.table-row') or soup.select('.tmTable.table-row')
        if not table_rows:
            error_texts = ['error', 'something went wrong', 'page not found']
            for error in error_texts:
                if error in html_content.lower():
                    logger.warning(f"Error page detected: '{error}'")
                    return {'b': 0, 'c1': "", 'c2': "", 'c3': "", 'SL': 0}
        
        # Get trademark count from h1.mr-auto
        trademark_count_value = 0
        count_heading = soup.find('h1', class_='mr-auto')
        if count_heading:
            count_text = count_heading.get_text()
            logger.info(f"Count heading text: {count_text}")
            match = re.search(r'About (\d{1,3}(?:,\d{3})*)', count_text)
            if match:
                trademark_count_value = int(match.group(1).replace(',', ''))
                logger.info(f"Extracted trademark count: {trademark_count_value}")
            else:
                # If no match found but text contains "found", set to 0 (valid no results page)
                if 'found' in count_text.lower():
                    trademark_count_value = 0
                    logger.info("No trademarks found for this brand")
        
        # If no trademarks found, return early
        if trademark_count_value == 0 and not table_rows:
            return {'b': 0, 'c1': "", 'c2': "", 'c3': "", 'SL': 0}
        
        # Get table rows - using the correct main selector
        logger.info("Searching for table rows...")
        if not table_rows:  # We already did this check above, but keeping for clarity
            table_rows = soup.select('a.table-row.tmTable')
        if not table_rows:
            table_rows = soup.select('a.table-row')
        if not table_rows:
            table_rows = soup.select('.tmTable.table-row')
        
        logger.info(f"Found {len(table_rows)} trademark entries")
        
        # Debug HTML if no rows found
        if not table_rows:
            logger.warning("No table rows found! Dumping HTML snippet:")
            html_snippet = html_content[:500] + "..." if len(html_content) > 500 else html_content
            logger.warning(html_snippet)
        
        trademarks = []
        owner_names = []
        statuses = []
        reg_dates = []
        exp_dates = []
        trademark_numbers = []
        classes = []
        
        # Extract data from each row
        for idx, row in enumerate(table_rows):
            logger.info(f"Processing row {idx+1}/{len(table_rows)}")
            
            # Try to find trademark name
            try:
                # Using exact selector provided
                name_element = row.select_one('p.font-bold.max-w-\\[300px\\]')
                
                if name_element:
                    # Get all spans within the element and join them
                    spans = name_element.find_all('span')
                    if spans:
                        trademark_name = ' '.join(span.get_text().strip() for span in spans)
                    else:
                        trademark_name = name_element.get_text().strip()
                else:
                    # Try alternative selector
                    name_element = row.select_one('td:nth-child(2) p.font-bold')
                    trademark_name = name_element.get_text().strip() if name_element else 'N/A'
                
                logger.info(f"Extracted trademark name: {trademark_name}")
            except Exception as e:
                logger.warning(f"Failed to extract trademark name: {str(e)}")
                trademark_name = 'N/A'
            
            # Try to find owner name
            try:
                # Using exact selector provided
                owner_element = row.select_one('p.text-sm.font-normal')
                
                if not owner_element:
                    # Try alternative selector from parent elements
                    owner_element = row.select_one('td.p-5 p.text-sm.font-normal')
                
                owner_name = owner_element.get_text().strip() if owner_element else 'N/A'
                logger.info(f"Extracted owner name: {owner_name}")
            except Exception as e:
                logger.warning(f"Failed to extract owner name: {str(e)}")
                owner_name = 'N/A'
            
            # 修改商标编号提取方法，直接查找具有font-bold类的段落
            try:
                # 首先尝试直接找到存储商标编号的<p class="font-bold">标签
                tm_number_element = row.select_one('td.p-5 p.font-bold') or row.select_one('p.font-bold:not(.max-w-\\[300px\\])')
                if tm_number_element:
                    tm_number = tm_number_element.get_text().strip()
                    # 验证是否是商标编号（通常是数字）
                    if re.match(r'^\d+$', tm_number):
                        logger.info(f"Extracted trademark number: {tm_number}")
                    else:
                        # 如果不是纯数字，尝试使用原来的方法
                        tm_number_elements = row.select('p.font-bold')
                        if len(tm_number_elements) >= 4:
                            tm_number = tm_number_elements[3].get_text().strip()
                        else:
                            tm_number_element = row.select_one('td:nth-child(2) p.font-bold:nth-of-type(2)')
                            tm_number = tm_number_element.get_text().strip() if tm_number_element else 'N/A'
                else:
                    # 回退到原来的方法
                    tm_number_elements = row.select('p.font-bold')
                    if len(tm_number_elements) >= 4:
                        tm_number = tm_number_elements[3].get_text().strip()
                    else:
                        tm_number_element = row.select_one('td:nth-child(2) p.font-bold:nth-of-type(2)')
                        tm_number = tm_number_element.get_text().strip() if tm_number_element else 'N/A'
                
                logger.info(f"Extracted trademark number: {tm_number}")
                trademark_numbers.append(tm_number)
            except Exception as e:
                logger.warning(f"Failed to extract trademark number: {str(e)}")
                trademark_numbers.append('N/A')
            
            # Try to find status
            try:
                # Using exact selector provided
                status_element = row.select_one('.status-live-registered')
                if not status_element:
                    status_element = row.select_one('td:nth-child(3) .flex:nth-child(1) span')
                
                status = status_element.get_text().strip() if status_element else 'N/A'
                logger.info(f"Extracted status: {status}")
            except Exception as e:
                logger.warning(f"Failed to extract status: {str(e)}")
                status = 'N/A'
            
            # Try to find registration date
            try:
                # Using exact selector provided
                reg_date_element = row.select_one('p.text-xs.mb-7')
                if reg_date_element:
                    reg_date = reg_date_element.get_text().strip()
                    # Extract date from "on XX XXX XXXX" format
                    reg_date_match = re.search(r'on\s+(\d{1,2}\s+\w+\s+\d{4})', reg_date)
                    reg_date = reg_date_match.group(1) if reg_date_match else reg_date
                else:
                    reg_date = 'N/A'
                
                logger.info(f"Extracted registration date: {reg_date}")
                reg_dates.append(reg_date)
            except Exception as e:
                logger.warning(f"Failed to extract registration date: {str(e)}")
                reg_dates.append('N/A')
            
            # Try to find expiration date
            try:
                # Using selector for expiration date
                exp_date_element = row.select_one('td:nth-child(3) .flex.items-center.text-xs p.font-bold')
                if exp_date_element:
                    exp_date = exp_date_element.get_text().strip()
                else:
                    exp_date = 'N/A'
                
                logger.info(f"Extracted expiration date: {exp_date}")
                exp_dates.append(exp_date)
            except Exception as e:
                logger.warning(f"Failed to extract expiration date: {str(e)}")
                exp_dates.append('N/A')
            
            # Try to find class
            try:
                # Using selector for class
                class_element = row.select_one('p.text-xs.whitespace-nowrap')
                if class_element:
                    class_info = class_element.get_text().strip()
                else:
                    class_info = 'N/A'
                
                logger.info(f"Extracted class info: {class_info}")
                classes.append(class_info)
            except Exception as e:
                logger.warning(f"Failed to extract class info: {str(e)}")
                classes.append('N/A')
            
            trademarks.append(trademark_name)
            owner_names.append(owner_name)
            statuses.append(status)
            
            logger.info(f"商标名称: {trademark_name}")
            logger.info(f"商标持有者: {owner_name}")
            logger.info(f"商标编号: {trademark_numbers[-1] if trademark_numbers else 'N/A'}")
            logger.info(f"状态: {status}")
            logger.info(f"注册日期: {reg_dates[-1] if reg_dates else 'N/A'}")
            logger.info(f"到期日期: {exp_dates[-1] if exp_dates else 'N/A'}")
            logger.info(f"类目: {classes[-1] if classes else 'N/A'}")
            logger.info('---------------------')
        
        # Create array of matching items
        q = []
        normalized_brand = normalize_brand(brand_name)
        logger.info(f'Normalized brand: {normalized_brand}')
        
        for i, trademark in enumerate(trademarks):
            formatted_trademark = normalize_brand(trademark)
            
            if formatted_trademark == normalized_brand:
                q.append({
                    'name': trademark,
                    'owner': owner_names[i],
                    'status': statuses[i]
                })
        
        # Calculate values based on match count
        match_count = len(q)
        logger.info(f"Found {match_count} matching trademarks")
        
        if match_count > 3:
            b = 4
            c1 = ""
            c2 = ""
            c3 = ""
        elif match_count == 3:
            live_or_registered_count = sum(1 for item in q if 'Live' in item['status'] or 'Registered' in item['status'])
            
            if live_or_registered_count >= 2:
                b = 4
                c1 = ""
                c2 = ""
                c3 = ""
            else:
                c1 = q[0]['owner']
                c2 = q[1]['owner']
                c3 = q[2]['owner']
                b = 3
        elif match_count == 2:
            c1 = q[0]['owner']
            c2 = q[1]['owner']
            c3 = ""
            b = 2
        elif match_count == 1:
            c1 = q[0]['owner']
            c2 = ""
            c3 = ""
            b = 1
        else:
            c1 = ""
            c2 = ""
            c3 = ""
            b = 0
        
        logger.info(f"b={b}, C1={c1}, C2={c2}, C3={c3}, Trademark Count={trademark_count_value}")
        
        return {
            'b': b,
            'c1': c1,
            'c2': c2,
            'c3': c3,
            'SL': trademark_count_value
        }
    
    except Exception as e:
        logger.error(f"Error extracting trademark data: {str(e)}")
        return {'b': 0, 'c1': "", 'c2': "", 'c3': "", 'SL': 0}

# 添加处理品牌子集的函数
def process_brand_subset(brands_df, start_idx, end_idx, thread_id):
    """处理品牌的子集，每个线程调用此函数"""
    logger.info(f"Thread {thread_id}: Processing brands from index {start_idx} to {end_idx}")
    
    # 创建线程特定的文件名
    thread_result_file = f'Trademark_Results_thread_{thread_id}.xlsx'
    thread_df_4_file = f'4_thread_{thread_id}.xlsx'
    thread_df_2_file = f'2_thread_{thread_id}.xlsx'
    thread_df_1_file = f'1_thread_{thread_id}.xlsx'
    thread_df_0_file = f'0_thread_{thread_id}.xlsx'
    thread_timeout_file = f'timeout_thread_{thread_id}.xlsx'
    checkpoint_file = f'checkpoint_thread_{thread_id}.txt'
    checkpoint_data_file = f'checkpoint_data_thread_{thread_id}.json'
    
    # 尝试加载已存在的线程结果文件
    results_df = pd.DataFrame(columns=brands_df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    df_4 = pd.DataFrame(columns=brands_df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    df_2 = pd.DataFrame(columns=brands_df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    df_1 = pd.DataFrame(columns=brands_df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    df_0 = pd.DataFrame(columns=brands_df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    df_timeout = pd.DataFrame(columns=brands_df.columns.tolist() + ['reason'])
    
    # 加载已有的结果文件
    logger.info(f"Thread {thread_id}: Checking for existing result files")
    try:
        if os.path.exists(thread_result_file):
            logger.info(f"Thread {thread_id}: Loading existing results from {thread_result_file}")
            results_df = pd.read_excel(thread_result_file, engine='openpyxl')
            
        if os.path.exists(thread_df_4_file):
            logger.info(f"Thread {thread_id}: Loading existing category 4 results")
            df_4 = pd.read_excel(thread_df_4_file, engine='openpyxl')
            
        if os.path.exists(thread_df_2_file):
            logger.info(f"Thread {thread_id}: Loading existing category 2 results")
            df_2 = pd.read_excel(thread_df_2_file, engine='openpyxl')
            
        if os.path.exists(thread_df_1_file):
            logger.info(f"Thread {thread_id}: Loading existing category 1 results")
            df_1 = pd.read_excel(thread_df_1_file, engine='openpyxl')
            
        if os.path.exists(thread_df_0_file):
            logger.info(f"Thread {thread_id}: Loading existing category 0 results")
            df_0 = pd.read_excel(thread_df_0_file, engine='openpyxl')
            
        if os.path.exists(thread_timeout_file):
            logger.info(f"Thread {thread_id}: Loading existing timeout results")
            df_timeout = pd.read_excel(thread_timeout_file, engine='openpyxl')
            
        logger.info(f"Thread {thread_id}: Successfully loaded previous results. Main results: {len(results_df)} rows")
    except Exception as e:
        logger.error(f"Thread {thread_id}: Error loading previous results: {str(e)}, will start fresh")
        
    # 尝试从断点文件恢复进度
    thread_start_idx = start_idx
    processed_items = set()  # 存储已处理的品牌+ASIN组合，避免重复处理
    
    try:
        if os.path.exists(checkpoint_file):
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_content = f.read().strip()
                parts = checkpoint_content.split('|')
                if len(parts) >= 1:
                    thread_start_idx = int(parts[0])
                    if thread_start_idx < start_idx:
                        thread_start_idx = start_idx
                    if thread_start_idx > end_idx:
                        thread_start_idx = start_idx
                
                # 如果断点文件包含已处理的品牌信息
                if len(parts) >= 2:
                    processed_items_str = parts[1]
                    if processed_items_str:
                        processed_items = set(processed_items_str.split(','))
                
                logger.info(f"Thread {thread_id}: Resuming from checkpoint at index {thread_start_idx}")
                logger.info(f"Thread {thread_id}: Found {len(processed_items)} processed items in checkpoint")
    except Exception as e:
        logger.error(f"Thread {thread_id}: Error reading checkpoint: {str(e)}")
        logger.info(f"Thread {thread_id}: Starting from index {start_idx}")
    
    # 如果有已处理的结果文件，扫描并添加到已处理品牌集合
    if not results_df.empty and 'Brand' in results_df.columns:
        # 检查是否有ASIN列
        if 'ASIN' in results_df.columns:
            for _, row in results_df.iterrows():
                item_key = f"{row['Brand']}|{row['ASIN']}"
                processed_items.add(item_key)
        else:
            # 兼容旧版本的结果文件
            for brand in results_df['Brand'].unique():
                processed_items.add(brand)
        logger.info(f"Thread {thread_id}: Added {len(processed_items)} processed items from previous results")
    
    # 浏览器初始化重试次数
    browser_init_retries = 3
    driver = None
    
    # 标记最近是否出现失败，用于10分钟等待逻辑
    recent_failure = False
    
    for browser_attempt in range(browser_init_retries):
        try:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument(f'user-agent={get_random_ua()}')
            
            # 添加自定义请求头
            for key, value in get_headers().items():
                chrome_options.add_argument(f'--header={key}: {value}')
            
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            chrome_options.add_argument('--blink-settings=imagesEnabled=false')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--enable-features=NetworkService,NetworkServiceInProcess')
            
            # 初始化Chrome驱动
            driver = webdriver.Chrome(options=chrome_options)
            logger.info(f"Thread {thread_id}: Browser opened successfully")
            
            # 设置窗口大小
            driver.set_window_size(1366, 768)
            
            # 先访问baidu建立历史记录
            logger.info(f"Thread {thread_id}: Visiting baidu.com...")
            max_retries = 3
            retry_count = 0
            baidu_success = False
            
            while retry_count < max_retries and not baidu_success:
                try:
                    driver.get('https://www.baidu.com')
                    baidu_success = True
                    break
                except Exception as e:
                    retry_count += 1
                    logger.warning(f"Thread {thread_id}: Attempt {retry_count}/{max_retries} to visit baidu.com failed: {str(e)}")
                    if retry_count < max_retries:
                        wait_time = 1 + random.random()  # 1-2秒的随机延迟
                        time.sleep(wait_time)
                    else:
                        logger.error(f"Thread {thread_id}: Failed to visit baidu.com, continuing anyway...")
            
            # 然后访问Trademarkia主页设置cookies
            logger.info(f"Thread {thread_id}: Visiting trademarkia.com...")
            retry_count = 0
            tm_success = False
            
            while retry_count < max_retries and not tm_success:
                try:
                    driver.get('https://www.trademarkia.com/')
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, 'body'))
                    )
                    
                    try:
                        cookie_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'accept') or contains(text(), 'Allow')]")
                        if cookie_buttons:
                            cookie_buttons[0].click()
                            logger.info(f"Thread {thread_id}: Clicked on cookie acceptance button")
                    except Exception as cookie_e:
                        logger.warning(f"Thread {thread_id}: Error handling cookie popup: {str(cookie_e)}")
                    
                    tm_success = True
                    break
                except Exception as e:
                    retry_count += 1
                    logger.warning(f"Thread {thread_id}: Attempt {retry_count}/{max_retries} to visit trademarkia.com failed: {str(e)}")
                    if retry_count < max_retries:
                        wait_time = 1 + random.random()  # 1-2秒的随机延迟
                        time.sleep(wait_time)
                    else:
                        logger.error(f"Thread {thread_id}: Failed to visit trademarkia.com, continuing anyway...")
            
            # 如果成功初始化浏览器和访问基础页面，跳出循环
            if baidu_success or tm_success:
                logger.info(f"Thread {thread_id}: Browser successfully initialized")
                break
                
        except Exception as e:
            logger.error(f"Thread {thread_id}: Browser initialization attempt {browser_attempt+1}/{browser_init_retries} failed: {str(e)}")
            
            # 确保关闭浏览器以释放资源
            try:
                if driver:
                    driver.quit()
                    driver = None
            except:
                pass
                
            # 如果不是最后一次尝试，等待后重试
            if browser_attempt < browser_init_retries - 1:
                wait_time = 1 + random.random()  # 1-2秒的随机延迟
                logger.info(f"Thread {thread_id}: Waiting {wait_time:.2f} seconds before trying to initialize browser again...")
                time.sleep(wait_time)
    
    # 检查是否成功初始化浏览器
    if not driver:
        logger.error(f"Thread {thread_id}: Failed to initialize browser after {browser_init_retries} attempts, thread exiting")
        return {
            'thread_id': thread_id,
            'error': "Failed to initialize browser",
            'results_df': results_df,
            'df_4': df_4,
            'df_2': df_2,
            'df_1': df_1,
            'df_0': df_0,
            'df_timeout': df_timeout
        }
    
    try:
        # 处理分配给这个线程的每个品牌
        idx = thread_start_idx
        is_first_search = True  # 标记是否为第一次搜索
        
        while idx <= end_idx:
            try:
                # 检查是否需要等待10分钟（由于之前的失败）
                if recent_failure:
                    logger.info(f"Thread {thread_id}: Waiting 10 minutes after previous failure before continuing...")
                    time.sleep(600)  # 10分钟 = 600秒
                    
                    # 尝试重新初始化浏览器
                    try:
                        if driver:
                            driver.quit()
                    except:
                        pass
                    
                    logger.info(f"Thread {thread_id}: Reopening browser after 10-minute wait...")
                    chrome_options = Options()
                    chrome_options.add_argument(f'user-agent={get_random_ua()}')
                    
                    # 添加自定义请求头
                    for key, value in get_headers().items():
                        chrome_options.add_argument(f'--header={key}: {value}')
                    
                    chrome_options.add_argument('--log-level=3')
                    chrome_options.add_argument('--silent')
                    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
                    chrome_options.add_argument('--blink-settings=imagesEnabled=false')
                    chrome_options.add_argument('--disable-extensions')
                    chrome_options.add_argument('--no-sandbox')
                    chrome_options.add_argument('--disable-gpu')
                    chrome_options.add_argument('--disable-dev-shm-usage')
                    
                    driver = webdriver.Chrome(options=chrome_options)
                    driver.set_window_size(1366, 768)
                    
                    # 访问初始页面
                    driver.get('https://www.baidu.com')
                    time.sleep(2)
                    driver.get('https://www.trademarkia.com/')
                    
                    recent_failure = False  # 重置失败标记
                
                if idx >= len(brands_df):
                    break
                    
                row = brands_df.iloc[idx]
                brand = row['Brand']
                
                # 检查是否已经处理过该品牌+ASIN组合
                item_key = f"{brand}|{row['ASIN']}" if 'ASIN' in row else brand
                if item_key in processed_items:
                    logger.info(f"Thread {thread_id}: Item '{item_key}' already processed, skipping")
                    idx += 1
                    continue
                
                logger.info(f"Thread {thread_id}: Processing brand: {brand} ({idx-start_idx+1}/{end_idx-start_idx+1})")
                
                # 在处理新品牌前，确保浏览器处于干净状态
                logger.info(f"Thread {thread_id}: Ensuring browser is in clean state before processing brand '{brand}'")
                try:
                    # 强制刷新主页面以清除任何状态
                    driver.get('https://www.trademarkia.com/')
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    
                    # 检查是否有cookie对话框并接受
                    try:
                        cookie_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'accept') or contains(text(), 'Allow')]")
                        if cookie_buttons:
                            cookie_buttons[0].click()
                            logger.info(f"Thread {thread_id}: Clicked on cookie acceptance button")
                    except Exception as cookie_e:
                        logger.warning(f"Thread {thread_id}: Error handling cookie popup: {str(cookie_e)}")
                    
                    # 等待页面完全稳定
                    time.sleep(2)
                    logger.info(f"Thread {thread_id}: Browser state reset complete")
                except Exception as reset_e:
                    logger.warning(f"Thread {thread_id}: Failed to reset browser state: {str(reset_e)}")
                
                # 添加随机延迟1-2秒
                delay_time = 1 + random.random()
                logger.info(f"Thread {thread_id}: Adding delay of {delay_time:.2f} seconds")
                time.sleep(delay_time)
                
                # 搜索商标
                logger.info(f"Thread {thread_id}: Starting search for brand: {brand}")
                html_content = search_trademark(brand, driver, max_retries=3, is_first_search=False)
                
                # 增加验证步骤，确保搜索请求已完成
                search_completed = False
                max_wait = 30  # 最多等待30秒
                wait_interval = 0.5  # 每0.5秒检查一次
                wait_count = 0
                
                while not search_completed and wait_count < (max_wait / wait_interval):
                    # 检查页面是否包含搜索结果或无结果消息
                    current_url = driver.current_url
                    page_source = driver.page_source
                    
                    if 'search' in current_url.lower() and len(page_source) > 1000:
                        # 检查是否有加载指示器
                        loading_indicators = driver.find_elements(By.CSS_SELECTOR, ".loading, .spinner, .progress")
                        if not loading_indicators:
                            logger.info(f"Thread {thread_id}: Search completed for brand '{brand}'")
                            search_completed = True
                        else:
                            logger.info(f"Thread {thread_id}: Still loading, waiting...")
                    
                    if not search_completed:
                        time.sleep(wait_interval)
                        wait_count += 1
                
                if not search_completed:
                    logger.warning(f"Thread {thread_id}: Search may not have completed properly for '{brand}' after waiting {max_wait} seconds")
                
                # 如果获取到内容，处理它
                if html_content:
                    # 在处理数据前再次验证内容与当前品牌相关
                    if brand.lower() not in html_content.lower() and urllib.parse.quote(brand.lower()) not in driver.current_url.lower():
                        logger.warning(f"Thread {thread_id}: Content validation failed - brand '{brand}' not found in response or URL!")
                        logger.warning(f"Thread {thread_id}: Current URL: {driver.current_url}")
                        # 强制刷新并重试
                        try:
                            driver.get('https://www.trademarkia.com/')
                            time.sleep(2)
                            html_content = search_trademark(brand, driver, max_retries=2, is_first_search=False)
                            logger.info(f"Thread {thread_id}: Retried search after content validation failure")
                        except Exception as retry_e:
                            logger.error(f"Thread {thread_id}: Retry failed: {str(retry_e)}")
                    
                    # 处理商标数据
                    logger.info(f"Thread {thread_id}: Processing trademark data for brand '{brand}'")
                    result = extract_trademark_data(html_content, brand)
                    
                    # 创建一行添加到适当的类别dataframe
                    row_data = row.to_dict()
                    row_data.update({
                        'b': result['b'],
                        'c1': result['c1'],
                        'c2': result['c2'],
                        'c3': result['c3'],
                        'SL': result['SL']
                    })
                    
                    # 添加到主结果DataFrame
                    new_row_df = pd.DataFrame([row_data])
                    if not results_df.empty and not new_row_df.empty:
                        results_df = pd.concat([results_df, new_row_df], ignore_index=True)
                    elif not new_row_df.empty:
                        results_df = new_row_df.copy()
                    
                    # 确定基于b值和Live状态存储到哪个类别dataframe
                    b_value = result['b']
                    
                    # 检查找到的商标的Live状态
                    soup = BeautifulSoup(html_content, 'html.parser')
                    table_rows = soup.select('a.table-row.tmTable') or soup.select('a.table-row') or soup.select('.tmTable.table-row')
                    
                    # 提取所有状态并检查"Live"
                    statuses = []
                    normalized_brand_name = normalize_brand(brand)
                    logger.info(f"Thread {thread_id}: Checking Live status for normalized brand: {normalized_brand_name}")
                    
                    for tr in table_rows:
                        status_element = tr.select_one('.status-live-registered') or tr.select_one('td:nth-child(3) .flex:nth-child(1) span')
                        if status_element:
                            status = status_element.get_text().strip()
                            name_element = tr.select_one('p.font-bold.max-w-\\[300px\\]')
                            if name_element:
                                row_trademark = name_element.get_text().strip()
                                normalized_row_trademark = normalize_brand(row_trademark)
                                if normalized_brand_name == normalized_row_trademark:
                                    statuses.append(status)
                                    logger.info(f"Thread {thread_id}: Found matching trademark '{row_trademark}' with status: {status}")
                    
                    live_count = sum(1 for status in statuses if 'Live' in status or 'Registered' in status)
                    logger.info(f"Thread {thread_id}: Found {live_count} live/registered trademarks out of {len(statuses)} matches")
                    
                    # 基于b值和live计数确定保存到哪个文件
                    if b_value == 4:
                        df_4 = pd.concat([df_4, pd.DataFrame([row_data])], ignore_index=True) if not df_4.empty else pd.DataFrame([row_data])
                        logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 4.xlsx (b={b_value})")
                    elif b_value == 3:
                        if live_count >= 2:
                            df_2 = pd.concat([df_2, pd.DataFrame([row_data])], ignore_index=True) if not df_2.empty else pd.DataFrame([row_data])
                            logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 2.xlsx (b={b_value}, live_count={live_count})")
                        elif live_count == 1:
                            df_1 = pd.concat([df_1, pd.DataFrame([row_data])], ignore_index=True) if not df_1.empty else pd.DataFrame([row_data])
                            logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 1.xlsx (b={b_value}, live_count={live_count})")
                        else:
                            df_0 = pd.concat([df_0, pd.DataFrame([row_data])], ignore_index=True) if not df_0.empty else pd.DataFrame([row_data])
                            logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 0.xlsx (b={b_value}, live_count={live_count})")
                    elif b_value == 2 or b_value == 1:
                        if live_count >= 1:
                            df_1 = pd.concat([df_1, pd.DataFrame([row_data])], ignore_index=True) if not df_1.empty else pd.DataFrame([row_data])
                            logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 1.xlsx (b={b_value}, live_count={live_count})")
                        else:
                            df_0 = pd.concat([df_0, pd.DataFrame([row_data])], ignore_index=True) if not df_0.empty else pd.DataFrame([row_data])
                            logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 0.xlsx (b={b_value}, live_count={live_count})")
                    else:  # b_value == 0
                        df_0 = pd.concat([df_0, pd.DataFrame([row_data])], ignore_index=True) if not df_0.empty else pd.DataFrame([row_data])
                        logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 0.xlsx (b={b_value})")
                
                else:
                    # 处理未能搜索的情况 - 添加到超时列表
                    logger.warning(f"Thread {thread_id}: Failed to search for brand {brand} after 3 retries")
                    timeout_data = row.to_dict()
                    timeout_data.update({'reason': 'Search timeout after 3 retries'})
                    
                    # 添加到超时DataFrame
                    timeout_df_new = pd.DataFrame([timeout_data])
                    if not df_timeout.empty and not timeout_df_new.empty:
                        df_timeout = pd.concat([df_timeout, timeout_df_new], ignore_index=True)
                    elif not timeout_df_new.empty:
                        df_timeout = timeout_df_new.copy()
                    logger.info(f"Thread {thread_id}: Added {brand} to timeout list")
                    
                    # 设置最近出现失败标记，用于下一次循环前等待10分钟
                    recent_failure = True
                    
                    # 创建默认值行
                    row_data = row.to_dict()
                    row_data.update({
                        'b': 0,
                        'c1': "",
                        'c2': "",
                        'c3': "",
                        'SL': 0
                    })
                    
                    # 添加到主结果
                    new_row_df = pd.DataFrame([row_data])
                    if not results_df.empty and not new_row_df.empty:
                        results_df = pd.concat([results_df, new_row_df], ignore_index=True)
                    elif not new_row_df.empty:
                        results_df = new_row_df.copy()
                    
                    # 也添加到0.xlsx
                    if not df_0.empty and not new_row_df.empty:
                        df_0 = pd.concat([df_0, new_row_df], ignore_index=True)
                    elif not new_row_df.empty:
                        df_0 = new_row_df.copy()
                    logger.info(f"Thread {thread_id}: Added {brand} to thread-specific 0.xlsx (failed search)")
                
                # 将当前品牌+ASIN组合添加到已处理集合
                item_key = f"{brand}|{row['ASIN']}" if 'ASIN' in row else brand
                processed_items.add(item_key)
                
                # 保存所有中间结果到线程特定的文件
                with file_lock:
                    # 保存线程结果文件
                    results_df.to_excel(thread_result_file, index=False, engine='openpyxl')
                    
                    # 保存线程类别文件
                    if not df_4.empty:
                        df_4.to_excel(thread_df_4_file, index=False, engine='openpyxl')
                    if not df_2.empty:
                        df_2.to_excel(thread_df_2_file, index=False, engine='openpyxl')
                    if not df_1.empty:
                        df_1.to_excel(thread_df_1_file, index=False, engine='openpyxl')
                    if not df_0.empty:
                        df_0.to_excel(thread_df_0_file, index=False, engine='openpyxl')
                    
                    # 保存超时数据
                    if not df_timeout.empty:
                        df_timeout.to_excel(thread_timeout_file, index=False, engine='openpyxl')
                    
                    # 更新断点信息，包括当前索引和已处理的品牌+ASIN组合
                    with open(checkpoint_file, 'w', encoding='utf-8') as f:
                        processed_items_str = ','.join(processed_items)
                        f.write(f"{idx}|{processed_items_str}")
                        
                logger.info(f"Thread {thread_id}: Saved all results after processing {idx-start_idx+1}/{end_idx-start_idx+1} brands")
                
                # 每处理10个品牌，重新访问主页刷新会话
                if (idx - start_idx + 1) % 10 == 0:
                    logger.info(f"Thread {thread_id}: Refreshing session by revisiting homepage...")
                    refresh_success = False
                    refresh_attempts = 0
                    max_refresh_attempts = 3
                    
                    while not refresh_success and refresh_attempts < max_refresh_attempts:
                        try:
                            driver.get('https://www.trademarkia.com/')
                            WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.TAG_NAME, "body"))
                            )
                            refresh_success = True
                        except Exception as refresh_e:
                            refresh_attempts += 1
                            logger.warning(f"Thread {thread_id}: Failed to refresh session (attempt {refresh_attempts}): {str(refresh_e)}")
                            if refresh_attempts < max_refresh_attempts:
                                wait_time = 1 + random.random()  # 1-2秒的随机延迟
                                time.sleep(wait_time)
                
                # 移动到下一个品牌
                idx += 1
            
            except Exception as e:
                logger.error(f"Thread {thread_id}: Error processing brand at index {idx}: {str(e)}")
                
                # 添加到超时数据中
                try:
                    row = brands_df.iloc[idx]
                    brand = row['Brand']
                    timeout_data = row.to_dict()
                    timeout_data.update({'reason': f'Exception: {str(e)}'})
                    timeout_df_new = pd.DataFrame([timeout_data])
                    if not df_timeout.empty and not timeout_df_new.empty:
                        df_timeout = pd.concat([df_timeout, timeout_df_new], ignore_index=True)
                    elif not timeout_df_new.empty:
                        df_timeout = timeout_df_new.copy()
                    logger.info(f"Thread {thread_id}: Added {brand} to timeout list")
                    
                    # 添加到已处理项目列表，避免重复处理
                    item_key = f"{brand}|{row['ASIN']}" if 'ASIN' in row else brand
                    processed_items.add(item_key)
                except:
                    pass
                
                # 设置最近出现失败标记
                recent_failure = True
                
                # 检查浏览器是否仍然响应
                try:
                    driver.current_url  # 简单测试浏览器是否响应
                except:
                    # 如果浏览器不响应，尝试重新初始化
                    logger.error(f"Thread {thread_id}: Browser seems unresponsive, will restart after 10-minute wait")
                    try:
                        if driver:
                            driver.quit()
                            driver = None
                    except:
                        pass
                
                # 移动到下一个品牌
                idx += 1
                continue
        
        # 处理完成后保留断点文件，以便检查是否有未完成的品牌
        # 但要添加标记表示线程已完成所有工作
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            processed_items_str = ','.join(processed_items)
            f.write(f"{end_idx+1}|{processed_items_str}|COMPLETED")
            
        logger.info(f"Thread {thread_id}: Completed processing all assigned brands")
            
        # 返回线程特定的dataframes，以便主线程合并
        return {
            'thread_id': thread_id,
            'results_df': results_df,
            'df_4': df_4,
            'df_2': df_2,
            'df_1': df_1,
            'df_0': df_0,
            'df_timeout': df_timeout
        }
    
    except Exception as e:
        logger.error(f"Thread {thread_id}: Error during processing: {e}")
        return {
            'thread_id': thread_id,
            'error': str(e),
            'results_df': results_df,
            'df_4': df_4,
            'df_2': df_2,
            'df_1': df_1,
            'df_0': df_0,
            'df_timeout': df_timeout
        }
    
    finally:
        # 关闭浏览器
        try:
            if driver:
                driver.quit()
                logger.info(f"Thread {thread_id}: Browser closed")
        except:
            pass

def main():
    # 设置线程数
    num_threads = 1  # 可以根据需要调整
    
    # 尝试从命令行参数获取线程数
    if len(sys.argv) > 1:
        try:
            user_threads = int(sys.argv[1])
            if user_threads > 0:
                num_threads = user_threads
                logger.info(f"Using {num_threads} threads as specified in command line")
        except:
            pass
    
    # 读取Excel文件
    try:
        df = pd.read_excel('Brand.xlsx', engine='openpyxl')
        logger.info(f"Successfully loaded file with {len(df)} rows")
        
        # 检查并处理小写的"brand"列名
        if 'brand' in df.columns and 'Brand' not in df.columns:
            logger.info("Found lowercase 'brand' column, renaming to 'Brand'")
            df.rename(columns={'brand': 'Brand'}, inplace=True)
        
        # 检查必要的列是否存在
        if 'Brand' not in df.columns:
            logger.error("Error: 'Brand' or 'brand' column not found in the Excel file")
            return
    except Exception as e:
        logger.error(f"Error loading Excel file: {e}")
        return
    
    # 检查是否有未完成的运行，尝试恢复
    incomplete_runs = False
    thread_checkpoints = {}
    thread_processed_items = {}  # 改名以反映存储复合键
    
    for i in range(1, num_threads + 1):
        checkpoint_file = f'checkpoint_thread_{i}.txt'
        if os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    parts = content.split('|')
                    
                    if len(parts) >= 1:
                        last_idx = int(parts[0])
                        thread_checkpoints[i] = last_idx
                        
                        # 检查是否有已处理的品牌记录
                        if len(parts) >= 2:
                            items = parts[1].split(',') if parts[1] else []
                            thread_processed_items[i] = set(items)  # 存储品牌+ASIN复合键
                            
                        # 检查是否标记为已完成
                        completed = len(parts) >= 3 and parts[2] == 'COMPLETED'
                        if not completed:
                            incomplete_runs = True
                            logger.info(f"Thread {i} has an incomplete run, will resume from index {last_idx}")
            except Exception as e:
                logger.error(f"Error reading checkpoint for thread {i}: {e}")
    
    if incomplete_runs:
        logger.info("Detected incomplete previous run. Will attempt to resume.")
    
    # 分割数据集
    brands_per_thread = math.ceil(len(df) / num_threads)
    thread_ranges = []
    
    for i in range(num_threads):
        start_idx = i * brands_per_thread
        end_idx = min((i + 1) * brands_per_thread - 1, len(df) - 1)
        thread_ranges.append((start_idx, end_idx))
    
    logger.info(f"Splitting work across {num_threads} threads")
    for i, (start, end) in enumerate(thread_ranges):
        logger.info(f"Thread {i+1} will process brands from index {start} to {end} ({end-start+1} brands)")
    
    # 在启动线程前先检查并加载现有的结果文件
    logger.info("Checking for existing result files before starting threads...")
    existing_results_found = False
    
    # 检查是否存在最终的合并结果文件
    if os.path.exists('Trademark_Results.xlsx'):
        logger.warning("Final results file 'Trademark_Results.xlsx' exists. Will back it up before starting.")
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_file = f'Trademark_Results_backup_{timestamp}.xlsx'
            os.rename('Trademark_Results.xlsx', backup_file)
            logger.info(f"Backed up existing results to {backup_file}")
        except Exception as e:
            logger.error(f"Failed to back up results file: {e}")
    
    # 使用线程池执行多线程处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 启动线程
        future_to_thread = {
            executor.submit(process_brand_subset, df, start, end, i+1): i+1
            for i, (start, end) in enumerate(thread_ranges)
        }
        
        # 保存所有线程的结果
        thread_results = []
        
        # 收集线程完成的结果
        for future in concurrent.futures.as_completed(future_to_thread):
            thread_id = future_to_thread[future]
            try:
                result = future.result()
                thread_results.append(result)
                if 'error' in result and result['error']:
                    logger.error(f"Thread {thread_id} reported error: {result['error']}")
                else:
                    logger.info(f"Thread {thread_id} completed successfully")
            except Exception as e:
                logger.error(f"Thread {thread_id} generated an exception: {str(e)}")
                # 添加空结果确保合并步骤不会失败
                thread_results.append({
                    'thread_id': thread_id,
                    'error': str(e),
                    'results_df': pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL']),
                    'df_4': pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL']),
                    'df_2': pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL']),
                    'df_1': pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL']),
                    'df_0': pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL']),
                    'df_timeout': pd.DataFrame(columns=df.columns.tolist() + ['reason'])
                })
    
    # 合并所有线程的结果
    logger.info("Merging results from all threads...")
    
    # 创建最终结果dataframe
    final_results_df = pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    final_df_4 = pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    final_df_2 = pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    final_df_1 = pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    final_df_0 = pd.DataFrame(columns=df.columns.tolist() + ['b', 'c1', 'c2', 'c3', 'SL'])
    final_df_timeout = pd.DataFrame(columns=df.columns.tolist() + ['reason'])
    
    # 创建一个集合来跟踪已处理的品牌，避免重复
    processed_keys = set()
    
    # 合并每个线程的结果
    for result in thread_results:
        thread_id = result['thread_id']
        logger.info(f"Merging results from thread {thread_id}")
        
        # 从结果中提取品牌列以检查重复
        thread_results_df = result['results_df']
        
        if not thread_results_df.empty and 'Brand' in thread_results_df.columns:
            # 检查是否存在ASIN列，如果有使用Brand+ASIN作为唯一标识
            if 'ASIN' in thread_results_df.columns:
                # 创建唯一标识列
                thread_results_df['unique_key'] = thread_results_df.apply(
                    lambda row: f"{row['Brand']}|{row['ASIN']}", 
                    axis=1
                )
                
                # 获取唯一标识集合
                unique_keys = set(thread_results_df['unique_key'].unique())
                logger.info(f"Thread {thread_id}: Found {len(unique_keys)} unique items")
                
                # 创建或检查final_results_df中的unique_key列
                if not final_results_df.empty and 'Brand' in final_results_df.columns and 'ASIN' in final_results_df.columns:
                    if 'unique_key' not in final_results_df.columns:
                        final_results_df['unique_key'] = final_results_df.apply(
                            lambda row: f"{row['Brand']}|{row['ASIN']}", 
                            axis=1
                        )
                    
                    # 获取已处理的唯一标识集合
                    processed_keys = set(final_results_df['unique_key'].unique())
                else:
                    processed_keys = set()
                
                # 确定新的唯一标识
                new_keys = unique_keys - processed_keys
                logger.info(f"Thread {thread_id}: {len(new_keys)} items are new (not in previous threads)")
                
                if len(new_keys) < len(unique_keys):
                    # 有重复的项目，需要过滤
                    filtered_results = thread_results_df[thread_results_df['unique_key'].isin(new_keys)]
                    logger.info(f"Thread {thread_id}: Filtered out {len(thread_results_df) - len(filtered_results)} duplicate entries")
                    
                    # 更新最终结果
                    if not filtered_results.empty and not final_results_df.empty:
                        final_results_df = pd.concat([final_results_df, filtered_results], ignore_index=True)
                    elif not filtered_results.empty:
                        final_results_df = filtered_results.copy()
                else:
                    # 没有重复，直接添加全部
                    if not thread_results_df.empty and not final_results_df.empty:
                        final_results_df = pd.concat([final_results_df, thread_results_df], ignore_index=True)
                    elif not thread_results_df.empty:
                        final_results_df = thread_results_df.copy()
            else:
                # 兼容旧版本，使用Brand列去重
                unique_brands = set(thread_results_df['Brand'].unique())
                logger.info(f"Thread {thread_id}: Found {len(unique_brands)} unique brands")
                
                # 创建已处理品牌集合
                processed_keys = set()
                if not final_results_df.empty and 'Brand' in final_results_df.columns:
                    processed_keys = set(final_results_df['Brand'].unique())
                
                new_brands = unique_brands - processed_keys
                logger.info(f"Thread {thread_id}: {len(new_brands)} brands are new (not in previous threads)")
                
                if len(new_brands) < len(unique_brands):
                    # 有重复的品牌，需要过滤
                    filtered_results = thread_results_df[thread_results_df['Brand'].isin(new_brands)]
                    logger.info(f"Thread {thread_id}: Filtered out {len(thread_results_df) - len(filtered_results)} duplicate entries")
                    
                    # 更新最终结果
                    if not filtered_results.empty and not final_results_df.empty:
                        final_results_df = pd.concat([final_results_df, filtered_results], ignore_index=True)
                    elif not filtered_results.empty:
                        final_results_df = filtered_results.copy()
                else:
                    # 没有重复，直接添加全部
                    if not thread_results_df.empty and not final_results_df.empty:
                        final_results_df = pd.concat([final_results_df, thread_results_df], ignore_index=True)
                    elif not thread_results_df.empty:
                        final_results_df = thread_results_df.copy()
        
        # 合并类别dataframes，使用相同的过滤逻辑
        for df_name, df_obj in [('df_4', result['df_4']), ('df_2', result['df_2']), 
                               ('df_1', result['df_1']), ('df_0', result['df_0'])]:
            if not df_obj.empty and 'Brand' in df_obj.columns:
                # 使用正确的变量名称引用
                if df_name == 'df_4':
                    final_df = final_df_4
                elif df_name == 'df_2':
                    final_df = final_df_2
                elif df_name == 'df_1':
                    final_df = final_df_1
                elif df_name == 'df_0':
                    final_df = final_df_0
                
                # 检查是否存在ASIN列
                if 'ASIN' in df_obj.columns:
                    # 创建唯一标识列
                    if 'unique_key' not in df_obj.columns:
                        df_obj['unique_key'] = df_obj.apply(
                            lambda row: f"{row['Brand']}|{row['ASIN']}", 
                            axis=1
                        )
                    
                    # 为最终DataFrame创建unique_key列
                    if not final_df.empty and 'Brand' in final_df.columns and 'ASIN' in final_df.columns:
                        if 'unique_key' not in final_df.columns:
                            final_df['unique_key'] = final_df.apply(
                                lambda row: f"{row['Brand']}|{row['ASIN']}", 
                                axis=1
                            )
                        local_processed = set(final_df['unique_key'].unique())
                    else:
                        local_processed = set()
                    
                    # 获取唯一键集合
                    df_keys = set(df_obj['unique_key'].unique())
                    
                    # 过滤掉当前最终dataframe中已经存在的项目
                    new_df_keys = df_keys - local_processed
                    
                    if len(new_df_keys) < len(df_keys):
                        filtered_df = df_obj[df_obj['unique_key'].isin(new_df_keys)]
                    else:
                        filtered_df = df_obj
                else:
                    # 兼容旧版本，使用Brand列去重
                    local_processed = set(final_df['Brand']) if not final_df.empty and 'Brand' in final_df.columns else set()
                    
                    # 过滤掉当前最终dataframe中已经存在的品牌
                    df_brands = set(df_obj['Brand'].unique())
                    new_df_brands = df_brands - local_processed
                    
                    if len(new_df_brands) < len(df_brands):
                        filtered_df = df_obj[df_obj['Brand'].isin(new_df_brands)]
                    else:
                        filtered_df = df_obj
                
                # 合并结果
                if not filtered_df.empty:
                    if df_name == 'df_4':
                        if not final_df_4.empty:
                            final_df_4 = pd.concat([final_df_4, filtered_df], ignore_index=True)
                        else:
                            final_df_4 = filtered_df.copy()
                    elif df_name == 'df_2':
                        if not final_df_2.empty:
                            final_df_2 = pd.concat([final_df_2, filtered_df], ignore_index=True)
                        else:
                            final_df_2 = filtered_df.copy()
                    elif df_name == 'df_1':
                        if not final_df_1.empty:
                            final_df_1 = pd.concat([final_df_1, filtered_df], ignore_index=True)
                        else:
                            final_df_1 = filtered_df.copy()
                    elif df_name == 'df_0':
                        if not final_df_0.empty:
                            final_df_0 = pd.concat([final_df_0, filtered_df], ignore_index=True)
                        else:
                            final_df_0 = filtered_df.copy()
        
        # 合并超时数据，同样过滤重复
        if 'df_timeout' in result:
            if not result['df_timeout'].empty and 'Brand' in result['df_timeout'].columns:
                # 检查是否存在ASIN列
                if 'ASIN' in result['df_timeout'].columns:
                    # 创建唯一标识列
                    if 'unique_key' not in result['df_timeout'].columns:
                        result['df_timeout']['unique_key'] = result['df_timeout'].apply(
                            lambda row: f"{row['Brand']}|{row['ASIN']}", 
                            axis=1
                        )
                    
                    # 为最终超时DataFrame创建unique_key列
                    if not final_df_timeout.empty and 'Brand' in final_df_timeout.columns and 'ASIN' in final_df_timeout.columns:
                        if 'unique_key' not in final_df_timeout.columns:
                            final_df_timeout['unique_key'] = final_df_timeout.apply(
                                lambda row: f"{row['Brand']}|{row['ASIN']}", 
                                axis=1
                            )
                        timeout_local_processed = set(final_df_timeout['unique_key'].unique())
                    else:
                        timeout_local_processed = set()
                    
                    # 获取唯一键集合
                    timeout_keys = set(result['df_timeout']['unique_key'].unique())
                    
                    # 过滤掉已处理的超时项目
                    new_timeout_keys = timeout_keys - timeout_local_processed
                    
                    if len(new_timeout_keys) < len(timeout_keys):
                        filtered_timeout = result['df_timeout'][result['df_timeout']['unique_key'].isin(new_timeout_keys)]
                    else:
                        filtered_timeout = result['df_timeout']
                else:
                    # 兼容旧版本，使用Brand列去重
                    timeout_brands = set(result['df_timeout']['Brand'].unique())
                    timeout_local_processed = set(final_df_timeout['Brand']) if not final_df_timeout.empty and 'Brand' in final_df_timeout.columns else set()
                    
                    new_timeout_brands = timeout_brands - timeout_local_processed
                    
                    if len(new_timeout_brands) < len(timeout_brands):
                        filtered_timeout = result['df_timeout'][result['df_timeout']['Brand'].isin(new_timeout_brands)]
                    else:
                        filtered_timeout = result['df_timeout']
                
                # 合并结果
                if not filtered_timeout.empty:
                    if not final_df_timeout.empty:
                        final_df_timeout = pd.concat([final_df_timeout, filtered_timeout], ignore_index=True)
                    else:
                        final_df_timeout = filtered_timeout.copy()
    
    # 计算处理完成情况
    total_brands = len(df)
    processed_count = len(final_results_df)
    completion_percentage = (processed_count / total_brands) * 100 if total_brands > 0 else 0
    
    logger.info(f"Processing summary: {processed_count}/{total_brands} brands processed ({completion_percentage:.2f}%)")
    
    # 保存最终结果
    logger.info("Saving final merged results...")
    final_results_df.to_excel('Trademark_Results.xlsx', index=False, engine='openpyxl')
    final_df_4.to_excel('4.xlsx', index=False, engine='openpyxl')
    final_df_2.to_excel('2.xlsx', index=False, engine='openpyxl')
    final_df_1.to_excel('1.xlsx', index=False, engine='openpyxl')
    final_df_0.to_excel('0.xlsx', index=False, engine='openpyxl')
    
    # 保存超时数据
    if not final_df_timeout.empty:
        final_df_timeout.to_excel('timeout_results.xlsx', index=False, engine='openpyxl')
        logger.info(f"Saved {len(final_df_timeout)} timed out brands to timeout_results.xlsx")
    
    # 如果所有品牌都已处理，则清理线程特定的文件
    if processed_count >= total_brands:
        logger.info("All brands processed. Cleaning up thread-specific files...")
        for i in range(1, num_threads + 1):
            try:
                thread_files = [
                    f'Trademark_Results_thread_{i}.xlsx',
                    f'4_thread_{i}.xlsx',
                    f'2_thread_{i}.xlsx',
                    f'1_thread_{i}.xlsx',
                    f'0_thread_{i}.xlsx',
                    f'timeout_thread_{i}.xlsx',
                    f'checkpoint_thread_{i}.txt'
                ]
                
                for file in thread_files:
                    if os.path.exists(file):
                        os.remove(file)
                        logger.info(f"Deleted {file}")
            except Exception as e:
                logger.warning(f"Error cleaning up thread {i} files: {str(e)}")
    else:
        logger.info(f"Not all brands processed yet ({processed_count}/{total_brands}). Keeping thread files for future runs.")
    
    logger.info("Processing complete. Final results saved to category files.")

if __name__ == "__main__":
    main() 