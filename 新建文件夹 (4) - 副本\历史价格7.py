import requests
from bs4 import BeautifulSoup
import re
import time
import random
import pandas as pd
import sys
import os
import logging
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import json
import hashlib
import uuid
import platform
import subprocess

# 内置UI主题，避免依赖外部文件
class AmazonUITheme:
    """亚马逊产品分析工具统一界面主题"""
    
    # 颜色方案
    COLORS = {
        "primary": "#356cac",     # 深蓝色 - 主色
        "secondary": "#4a90e2",   # 亮蓝色 - 次要色
        "accent": "#f89406",      # 橙色 - 强调色
        "background": "#f5f5f5",  # 浅灰色 - 背景色
        "text": "#333333",        # 深灰色 - 文本色
        "light_text": "#666666",  # 中灰色 - 次要文本
        "border": "#dddddd",      # 边框色
        "success": "#28a745",     # 成功色
        "warning": "#ffc107",     # 警告色
        "error": "#dc3545",       # 错误色
        "white": "#ffffff",       # 白色
        "light_gray": "#f0f0f0"   # 更浅的灰色
    }
    
    # 字体设置
    FONTS = {
        "title": ("微软雅黑", 12, "bold"),
        "subtitle": ("微软雅黑", 11, "bold"),
        "body": ("微软雅黑", 10),
        "small": ("微软雅黑", 9),
        "menu": ("微软雅黑", 10),
        "button": ("微软雅黑", 10),
        "status": ("微软雅黑", 9)
    }
    
    # 尺寸和间距
    PADDING = {
        "frame": 15,      # 框架内边距
        "button": 8,      # 按钮内边距
        "widget": 5,      # 控件间距
        "section": 10,    # 区块间距
        "tiny": 2,        # 最小间距
    }
    
    @classmethod
    def get_image_path(cls):
        """获取图标路径"""
        # 确定工作目录
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 图标文件路径
        icon_path = os.path.join(base_dir, "icon.ico")
        if os.path.exists(icon_path):
            return icon_path
        return None
    
    @classmethod
    def setup_styles(cls):
        """设置通用ttk样式"""
        style = ttk.Style()
        
        # 设置全局主题
        try:
            style.theme_use("clam")  # 使用clam主题作为基础
        except:
            pass  # 如果主题不可用，使用默认主题
        
        # 背景配置
        style.configure("TFrame", background=cls.COLORS["background"])
        style.configure("TLabelframe", background=cls.COLORS["background"])
        style.configure("TLabelframe.Label", background=cls.COLORS["background"], 
                        foreground=cls.COLORS["primary"], font=cls.FONTS["subtitle"])
        
        # 按钮风格
        style.configure("TButton", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["button"],
                        padding=cls.PADDING["button"])
        
        style.map("TButton",
                  background=[('active', cls.COLORS["secondary"]), 
                              ('disabled', cls.COLORS["border"])],
                  foreground=[('disabled', cls.COLORS["light_text"])])
        
        # 次要按钮风格
        style.configure("Secondary.TButton", 
                        background=cls.COLORS["secondary"],
                        foreground=cls.COLORS["white"])
        
        # 强调按钮风格
        style.configure("Accent.TButton", 
                        background=cls.COLORS["accent"],
                        foreground=cls.COLORS["white"])
        
        # 标签风格
        style.configure("TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 标题标签风格
        style.configure("Title.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["primary"],
                        font=cls.FONTS["title"])
        
        # 子标题标签风格
        style.configure("Subtitle.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["secondary"],
                        font=cls.FONTS["subtitle"])
        
        # Entry风格
        style.configure("TEntry", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # Combobox风格
        style.configure("TCombobox", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # 复选框风格
        style.configure("TCheckbutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 单选按钮风格
        style.configure("TRadiobutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 进度条风格
        style.configure("TProgressbar", 
                        background=cls.COLORS["primary"],
                        troughcolor=cls.COLORS["light_gray"])
        
        # 分隔符风格
        style.configure("TSeparator", 
                        background=cls.COLORS["border"])
        
        # Treeview (表格) 风格
        style.configure("Treeview", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        font=cls.FONTS["body"])
        
        style.map("Treeview",
                  background=[('selected', cls.COLORS["secondary"])],
                  foreground=[('selected', cls.COLORS["white"])])
        
        style.configure("Treeview.Heading", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["subtitle"],
                        relief="flat")
        
        # Notebook (选项卡) 风格
        style.configure("TNotebook", 
                        background=cls.COLORS["background"],
                        tabmargins=[2, 5, 2, 0])
        
        style.configure("TNotebook.Tab", 
                        background=cls.COLORS["light_gray"],
                        foreground=cls.COLORS["text"],
                        padding=[10, 4],
                        font=cls.FONTS["body"])
        
        style.map("TNotebook.Tab",
                  background=[('selected', cls.COLORS["primary"])],
                  foreground=[('selected', cls.COLORS["white"])],
                  expand=[('selected', [1, 1, 1, 0])])
        
        return style
    
    @classmethod
    def setup_window(cls, root, title, size="800x600", resizable=(True, True), icon=True):
        """设置窗口基本属性"""
        root.title(title)
        root.geometry(size)
        root.resizable(resizable[0], resizable[1])
        root.configure(bg=cls.COLORS["background"])
        
        # 设置图标
        if icon:
            icon_path = cls.get_image_path()
            if icon_path and os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                except:
                    pass  # 图标设置失败时忽略
        
        # 设置样式
        cls.setup_styles()
        
        return root
    
    @classmethod
    def create_title_frame(cls, parent, title_text):
        """创建标题栏框架"""
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["frame"], 0))
        
        # 标题标签
        title_label = ttk.Label(title_frame, text=title_text, style="Title.TLabel")
        title_label.pack(side=tk.LEFT, pady=cls.PADDING["section"])
        
        # 添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(0, cls.PADDING["section"]))
        
        return title_frame
    
    @classmethod
    def create_footer_frame(cls, parent):
        """创建底部按钮框架"""
        # 先添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["section"], 0))
        
        footer_frame = ttk.Frame(parent)
        footer_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=cls.PADDING["section"])
        
        return footer_frame
    
    @classmethod
    def create_section_frame(cls, parent, title=None):
        """创建分区框架（可选带标题）"""
        if title:
            section_frame = ttk.LabelFrame(parent, text=title)
        else:
            section_frame = ttk.Frame(parent)
        
        section_frame.pack(fill=tk.BOTH, expand=True, padx=cls.PADDING["frame"], 
                          pady=cls.PADDING["section"])
        
        return section_frame

# 添加依赖检查函数
def check_and_install_dependencies():
    """检查并安装必要的依赖库"""
    try:
        import importlib
        import subprocess
        
        required_packages = {
            'selenium': 'selenium',
            'beautifulsoup4': 'bs4',
            'pandas': 'pandas',
            'requests': 'requests',
            'webdriver_manager': 'webdriver_manager'
        }
        
        missing_packages = []
        
        for package_name, import_name in required_packages.items():
            try:
                importlib.import_module(import_name)
            except ImportError:
                missing_packages.append(package_name)
        
        if missing_packages:
            # 简化输出，不显示具体的包名
            print("配置系统环境...")
            try:
                for package in missing_packages:
                    # 隐藏具体包名和安装过程
                    subprocess.check_call(
                        [sys.executable, "-m", "pip", "install", package],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                # 简化输出
                print("系统配置完成")
                # 重新导入所有模块
                for package_name, import_name in required_packages.items():
                    if package_name in missing_packages:
                        importlib.import_module(import_name)
                return True
            except Exception as e:
                # 隐藏错误信息
                print("系统配置失败，请联系管理员")
                return False
        # 简化输出
        print("系统检查完成")
        return True
    except Exception as e:
        # 隐藏错误信息
        print("环境检查发生错误")
        return False

# 检查并安装依赖
if not check_and_install_dependencies():
    print("初始化失败，程序退出")
    sys.exit(1)

# 导入所需的库
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from datetime import datetime, timedelta
import concurrent.futures
import threading
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import json
import hashlib
import uuid
import platform
import subprocess

# 自定义日志过滤器，用于保护敏感信息
class SensitiveInfoFilter(logging.Filter):
    def filter(self, record):
        if record.getMessage():
            # 过滤敏感信息的模式，但保留ASIN码
            patterns = [
                (r'https?://[^\s]+', '[URL]'),  # URL
                # 移除ASIN过滤，使ASIN码在日志中可见
                (r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', '[IP]'),  # IP地址
                (r'(?:[a-zA-Z]:\\|/)(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*', '[PATH]'),  # 文件路径
                (r'\\n', ''),  # 移除可能暴露内部逻辑的换行符
                (r'TypeError|ValueError|Exception|Error', '错误')  # 统一错误消息
            ]
            
            message = record.getMessage()
            for pattern, replacement in patterns:
                message = re.sub(pattern, replacement, message)
            
            # 更新记录的消息
            record.msg = message
            if hasattr(record, 'args') and record.args:
                record.args = ()  # 清除参数
        return True

# 禁用selenium日志
logging.getLogger('selenium').setLevel(logging.ERROR)  # 只显示错误，从WARNING提升到ERROR
logging.getLogger('urllib3').setLevel(logging.ERROR)   # 只显示错误，从WARNING提升到ERROR

# 设置应用主日志
logger = logging.getLogger('price_checker')
logger.setLevel(logging.WARNING)  # 提高日志级别，只显示警告和错误

# 添加处理器和过滤器
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))  # 简化格式，不显示时间和模块
logger.addHandler(handler)

# 添加敏感信息过滤器
info_filter = SensitiveInfoFilter()
logger.addFilter(info_filter)

# 线程本地存储，每个线程拥有自己的浏览器实例
thread_local = threading.local()

class KeepaHistoricalPriceChecker:
    def __init__(self, threads=1, marketplace="US", excel_file=None, verbose=False):
        self.excel_file = excel_file or "Amazon产品信息.xlsx"
        self.marketplace = marketplace  # 市场: US, CA, JP, UK, MX
        self.market_names = {
            "US": "美国",
            "CA": "加拿大",
            "JP": "日本",
            "UK": "英国",
            "MX": "墨西哥"
        }
        # 修改输出文件名，包含国家名称
        country_name = self.market_names.get(self.marketplace, "未知")
        # 仅使用文件名，不包含路径，保存到当前目录
        base_filename = os.path.basename(self.excel_file)
        base_name_without_ext = os.path.splitext(base_filename)[0]
        self.output_file = f"{base_name_without_ext}_{country_name}_历史价格信息.xlsx"
        self.checkpoint_file = f"{base_name_without_ext}_{country_name}_checkpoint.json"
        self.threads = threads  # 线程数量
        self.results_lock = threading.Lock()  # 用于结果字典的线程锁
        self.verbose = verbose  # 控制日志输出详细程度
        
        # 市场对应的货币符号
        self.currency_symbols = {
            "US": "$",
            "CA": "$",
            "JP": "¥",
            "UK": "£",
            "MX": "$"
        }
        
        # 运行状态控制
        self.running = False
        self.stop_requested = False
        
    def setup_selenium_browser(self):
        """设置并初始化Selenium浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            # 无头模式（不显示浏览器界面，在后台运行）
            chrome_options.add_argument('--headless=new')
            # WebGL相关设置
            chrome_options.add_argument('--enable-unsafe-swiftshader')
            # 设置用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
            
            # 设置语言和地区
            chrome_options.add_argument('--lang=zh-CN,zh;q=0.9,en;q=0.8')
            
            # 防检测设置
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 日志设置
            chrome_options.add_argument("--log-level=3")  # 仅显示致命错误
            chrome_options.add_argument("--silent")
            
            # 创建服务对象并设置日志抑制
            service = Service(log_output=os.devnull)
            
            # 创建webdriver
            driver = webdriver.Chrome(options=chrome_options, service=service)
            
            # 设置执行CDP命令绕过检测
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                "platform": "Windows"
            })
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                '''
            })
            
            return driver
        except Exception as e:
            # 隐藏具体错误信息
            logger.error("浏览器初始化失败")
            return None

    def get_thread_driver(self):
        """获取当前线程的浏览器驱动"""
        if not hasattr(thread_local, 'driver'):
            if self.verbose:
                # 简化日志，不显示线程名
                print("正在初始化...")
            thread_local.driver = self.setup_selenium_browser()
        return thread_local.driver

    def close_thread_driver(self):
        """关闭当前线程的浏览器驱动"""
        if hasattr(thread_local, 'driver') and thread_local.driver:
            thread_local.driver.quit()
            thread_local.driver = None
            if self.verbose:
                # 简化日志，不显示线程名
                print("资源已释放")

    def check_and_set_marketplace(self, driver):
        """
        检查并设置正确的市场（美国、加拿大、日本、英国或墨西哥）
        :param driver: Selenium浏览器驱动
        :return: 是否成功设置市场
        """
        try:
            if self.verbose:
                # 简化日志
                print(f"设置市场: {self.marketplace}")
            
            # 等待页面加载，确保域选择器元素可用
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "span.languageMenuImg[rel='domain']"))
            )
            
            # 获取当前域名标识
            domain_element = driver.find_element(By.CSS_SELECTOR, "span.languageMenuImg[rel='domain']")
            current_domain_text = domain_element.text.strip()
            
            # 获取目标域名，映射市场到域名扩展
            domain_mapping = {
                "US": ".com",
                "CA": ".ca",
                "JP": ".jp",
                "UK": ".uk",
                "MX": ".mx"
            }
            target_domain = domain_mapping.get(self.marketplace)
            
            if not target_domain:
                if self.verbose:
                    print(f"不支持的市场: {self.marketplace}")
                return False
                
            if self.verbose:
                print(f"当前域名标识: {current_domain_text}, 目标域名: {target_domain}")
            
            # 检查当前域名是否已经是目标域名
            if target_domain == current_domain_text:
                if self.verbose:
                    print(f"已在正确的市场: {self.marketplace} ({target_domain})")
                return True
                
            # 需要切换域名
            if self.verbose:
                print(f"需要切换域名从 {current_domain_text} 到 {target_domain}")
            
            # 点击域名菜单
            domain_element.click()
            time.sleep(1)  # 等待菜单显示
            
            # 选择相应的市场域名
            setting_mapping = {
                "US": "1",  # .com
                "CA": "6",  # .ca
                "JP": "5",  # .jp
                "UK": "2",  # .uk
                "MX": "11"  # .mx
            }
            
            setting_value = setting_mapping.get(self.marketplace)
            domain_selector = f"span[rel='domain'][setting='{setting_value}']"
            
            # 等待并点击目标域名选项
            WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, domain_selector))
            ).click()
            
            if self.verbose:
                print(f"已点击切换到 {target_domain}")
            
            # 等待页面重新加载
            time.sleep(5)
            
            # 再次验证域名是否已切换
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "span.languageMenuImg[rel='domain']"))
            )
            domain_element = driver.find_element(By.CSS_SELECTOR, "span.languageMenuImg[rel='domain']")
            new_domain_text = domain_element.text.strip()
            
            if new_domain_text == target_domain:
                if self.verbose:
                    print(f"成功切换到市场: {self.marketplace} ({target_domain})")
                return True
            else:
                if self.verbose:
                    print(f"域名切换失败，当前域名为: {new_domain_text}")
                return False
                
        except Exception as e:
            if self.verbose:
                print(f"设置市场时出错: {str(e)}")
            return False

    def load_asins_from_excel(self):
        """从Excel文件中读取ASIN列，支持任意大小写形式"""
        try:
            if not os.path.exists(self.excel_file):
                print(f"错误: 找不到Excel文件 {self.excel_file}")
                return []
            
            df = pd.read_excel(self.excel_file)
            # 检查列名，不区分大小写
            asin_col = None
            for col in df.columns:
                # 完全不区分大小写比较列名
                if col.upper() == 'ASIN':
                    asin_col = col
                    break
            
            if not asin_col:
                # 尝试查找更多可能的列名变体
                possible_variants = ['ASIN', 'asin', 'Asin', 'ASin', 'ProductID', '产品ID']
                variant_found = False
                
                for variant in possible_variants:
                    if variant in df.columns:
                        asin_col = variant
                        variant_found = True
                        print(f"使用列 '{variant}' 作为ASIN列")
                        break
                
                if not variant_found:
                    print(f"错误: Excel文件中没有找到ASIN列（尝试查找：ASIN, asin, Asin等）")
                    return []
            
            asins = df[asin_col].tolist()
            # 确保ASIN格式正确
            valid_asins = [str(asin).strip().upper() for asin in asins if not pd.isna(asin) and str(asin).strip()]
            
            print(f"从Excel文件中加载了 {len(valid_asins)} 个ASIN")
            return valid_asins
        except Exception as e:
            print(f"加载ASIN时出错: {str(e)}")
            return []

    def get_keepa_info_with_browser(self, asin):
        """
        使用当前线程的浏览器从keepa.com获取ASIN的历史最高价和断货时间
        :param asin: Amazon产品的ASIN码
        :return: 包含历史最高价和断货时间的字典
        """
        result = {
            "historical_highest_price": None,
            "historical_lowest_price": None,
            "out_of_stock_within_month": None
        }
        
        # 获取当前线程的浏览器
        driver = self.get_thread_driver()
        if not driver:
            print(f"无法为ASIN {asin} 设置浏览器，返回默认结果")
            return result
            
        try:
            # 构建URL并访问
            domain_mapping = {
                "US": "1",  # 美国
                "CA": "6",  # 加拿大
                "JP": "5",  # 日本
                "UK": "2",  # 英国
                "MX": "11"  # 墨西哥
            }
            marketplace_id = domain_mapping.get(self.marketplace, "5")  # 默认日本
            
            product_url = f"https://keepa.com/#!product/{marketplace_id}-{asin}"
            if self.verbose:
                print(f"线程 {threading.current_thread().name} 访问 {asin} Keepa URL: {product_url} (市场: {self.marketplace})")
            
            # 访问网页
            driver.get(product_url)
            
            # 等待页面加载，最多等待30秒
            if self.verbose:
                print(f"等待页面加载 (ASIN: {asin})...")
            
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查并设置正确的市场
            if not self.check_and_set_marketplace(driver):
                if self.verbose:
                    print(f"无法设置正确的市场 {self.marketplace}，继续使用当前设置...")
            
            # 延迟一些时间以确保JavaScript完全执行
            time.sleep(10)
            
            # 保存页面源代码
            page_source = driver.page_source
            
            # 用BeautifulSoup解析HTML
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 打印页面标题(仅在详细模式)
            if self.verbose:
                title = soup.find('title')
                if title:
                    print(f"页面标题: {title.text}")
            
            # 获取时间信息
            if self.verbose:
                print("查找时间信息...")
            
            # 在页面中查找时间信息
            page_text = soup.get_text()
            found_time_info = False
            
            # 首先检查页面中是否存在"上次价格变动"的文本
            if "上次价格变动" not in page_text:
                # 如果整个页面都没有出现"上次价格变动"字样，则认为断货时间超过15天
                result["out_of_stock_within_month"] = 2
                if self.verbose:
                    print("页面中没有找到'上次价格变动'信息，判断产品在15天外断货")
                found_time_info = True
            else:
                # 存在"上次价格变动"文本，继续查找具体的时间信息
                # 寻找类似"上次更新：2分钟前，上次价格变动: 6个月前"的文本
                # 优先检查"上次价格变动"的时间
                price_change_patterns = [
                    r'上次价格变动:\s*(\d+)\s*分钟前',
                    r'上次价格变动:\s*(\d+)\s*小时前',
                    r'上次价格变动:\s*(\d+)\s*天前',
                    r'上次价格变动:\s*(\d+)\s*周前',
                    r'上次价格变动:\s*(\d+)\s*个月前',
                    r'上次价格变动:\s*(\d+)\s*年前'
                ]
                
                # 首先尝试查找价格变动时间
                for pattern in price_change_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        time_value = int(match.group(1))
                        time_unit = pattern.split('\\s*')[-1].replace(')', '')
                        if self.verbose:
                            print(f"找到价格变动时间: {time_value} {time_unit}")
                        found_time_info = True
                        
                        # 判断时间是否在15天内
                        in_15_days = False
                        if '分钟' in pattern or '小时' in pattern:
                            in_15_days = True
                        elif '天' in pattern and time_value <= 15:
                            in_15_days = True
                        elif '周' in pattern and time_value <= 2:  # 1周和2周都小于15天 (2周=14天)
                            in_15_days = True
                        
                        if in_15_days:
                            result["out_of_stock_within_month"] = 1
                            if self.verbose:
                                print(f"基于价格变动时间信息 {time_value} {time_unit}，判断产品在15天内断货")
                        else:
                            result["out_of_stock_within_month"] = 2
                            if self.verbose:
                                print(f"基于价格变动时间信息 {time_value} {time_unit}，判断产品在15天外断货")
                        break
            
            # 尝试寻找历史最高价和最低价
            highest_price_elements = soup.find_all('tr', class_='tracking__suggestion-item')
            
            # 收集所有有效的价格元素（必须同时包含data-value属性和价格符号[$, ¥或£]）
            valid_prices = []
            
            for elem in highest_price_elements:
                if 'data-value' in elem.attrs:
                    try:
                        # 检查该元素是否包含价格符号 $, ¥ 或 £
                        has_price_sign = False
                        for td in elem.find_all('td'):
                            if '$' in td.text or '¥' in td.text or '£' in td.text:
                                has_price_sign = True
                                break
                        
                        # 只有包含价格符号的项目才是有效价格
                        if has_price_sign:
                            # 统一处理所有市场的价格格式，只提取数字部分
                            price_value = elem['data-value']
                            if self.verbose:
                                print(f"原始价格值: {price_value}")
                            
                            # 移除所有非数字字符（除了小数点）
                            price_value = re.sub(r'[^\d.]', '', price_value)
                            if self.verbose:
                                print(f"清理后的价格值: {price_value}")
                            
                            # 验证价格格式
                            if not re.match(r'^\d+\.?\d*$', price_value):
                                if self.verbose:
                                    print(f"警告: 价格格式无效: {price_value}")
                                continue
                                
                            price_value = float(price_value)
                            if self.verbose:
                                print(f"转换后的价格值: {price_value}")
                            valid_prices.append(price_value)
                    except (ValueError, TypeError) as e:
                        if self.verbose:
                            print(f"价格转换错误: {str(e)}")
            
            # 如果找到有效价格，设置最高价和最低价
            if valid_prices:
                result["historical_highest_price"] = max(valid_prices)
                result["historical_lowest_price"] = min(valid_prices)
                if self.verbose:
                    print(f"通过data-value找到历史最高价: {result['historical_highest_price']}")
                    print(f"通过data-value找到历史最低价: {result['historical_lowest_price']}")
            else:
                # 如果未找到有效价格，尝试传统方法
                for elem in highest_price_elements:
                    if elem.find('td') and '历史最高' in elem.find('td').text:
                        price_elem = elem.find_all('td')[-1]
                        if price_elem:
                            price_text = price_elem.text.strip()
                            if self.verbose:
                                print(f"原始价格文本: {price_text}")
                            
                            # 提取所有数字（包括小数点）
                            price_match = re.search(r'[\d,.]+', price_text)
                            if price_match:
                                price_value = price_match.group(0)
                                if self.verbose:
                                    print(f"匹配到的价格值: {price_value}")
                                
                                # 移除所有非数字字符（除了小数点）
                                price_value = re.sub(r'[^\d.]', '', price_value)
                                if self.verbose:
                                    print(f"清理后的价格值: {price_value}")
                                
                                # 验证价格格式
                                if not re.match(r'^\d+\.?\d*$', price_value):
                                    if self.verbose:
                                        print(f"警告: 价格格式无效: {price_value}")
                                    continue
                                    
                                result["historical_highest_price"] = float(price_value)
                                if self.verbose:
                                    print(f"找到历史最高价: {price_value}")
                                break
            
            # 如果在表格中找不到，尝试在statsTable中寻找
            if result["historical_highest_price"] is None:
                if self.verbose:
                    print("在statsTable中查找历史最高价...")
                stats_table = soup.find('table', id='statsTable')
                if stats_table:
                    highest_rows = stats_table.find_all('tr')
                    for row in highest_rows:
                        if row.find('td', class_='statsRow1') and '最高' in row.find('td', class_='statsRow1').text:
                            price_cells = row.find_all('td', class_='statsRow3')
                            if price_cells and len(price_cells) > 0:
                                price_text = price_cells[0].text.strip()
                                # 提取所有数字（包括小数点）
                                price_match = re.search(r'[\d,.]+', price_text)
                                if price_match:
                                    result["historical_highest_price"] = float(price_match.group(0))
                                    if self.verbose:
                                        print(f"从statsTable找到历史最高价: {price_text}")
                                    break
            
            # 如果没有通过时间信息找到断货状态，使用之前的方法检查
            if not found_time_info:
                # 检查断货信息 - 分析日期
                if self.verbose:
                    print("查找断货信息和日期...")
                
                # 获取当前日期
                today = datetime.now()
                
                # 中文月份映射到数字
                month_map = {
                    '一月': 1, '二月': 2, '三月': 3, '四月': 4, 
                    '五月': 5, '六月': 6, '七月': 7, '八月': 8, 
                    '九月': 9, '十月': 10, '十一月': 11, '十二月': 12
                }
                
                stats_table = soup.find('table', id='statsTable')
                if stats_table:
                    rows = stats_table.find_all('tr')
                    for row in rows:
                        # 查找"目前"行
                        first_cell = row.find('td', class_='statsRow1')
                        if first_cell and '目前' in first_cell.text:
                            # 查找是否有"无货"状态
                            row_text = row.text.lower()
                            if '无货' in row_text or 'out of stock' in row_text:
                                # 找到日期信息
                                date_info = None
                                
                                # 尝试找到statsRow中的日期
                                for i in range(2, 7):
                                    cell = row.find('td', class_=f'statsRow{i}')
                                    if cell:
                                        # 检查是否有日期格式（中文月份 日, 年）
                                        date_match = re.search(r'([\u4e00-\u9fa5]+)\s+(\d+),\s+(\d{4})', cell.text)
                                        if date_match:
                                            cn_month, day, year = date_match.groups()
                                            if cn_month in month_map:
                                                month = month_map[cn_month]
                                                try:
                                                    product_date = datetime(int(year), month, int(day))
                                                    date_info = product_date
                                                    days_diff = (today - product_date).days
                                                    if self.verbose:
                                                        print(f"找到断货日期: {product_date.strftime('%Y-%m-%d')}, 距今 {days_diff} 天")
                                                    
                                                    if days_diff <= 15:
                                                        result["out_of_stock_within_month"] = 1
                                                        if self.verbose:
                                                            print("产品在15天内断货")
                                                    else:
                                                        result["out_of_stock_within_month"] = 2
                                                        if self.verbose:
                                                            print("产品在15天外断货")
                                                    break
                                                except ValueError as e:
                                                    if self.verbose:
                                                        print(f"日期转换错误: {e}")
                                
                                # 如果没有具体日期，但确认是当前断货，默认为15天内
                                if date_info is None and result["out_of_stock_within_month"] is None:
                                    result["out_of_stock_within_month"] = 1
                                    if self.verbose:
                                        print("产品当前断货，但无法确定日期，默认为15天内")
                                
                                break
                                
            # 在结果最终确定后，输出一个简易摘要
            price_symbol = self.currency_symbols.get(self.marketplace, "$")
            highest_price = result.get("historical_highest_price")
            lowest_price = result.get("historical_lowest_price")
            out_of_stock = result.get("out_of_stock_within_month")
            
            stock_status = "未知"
            if out_of_stock == 1:
                stock_status = "15天内"
            elif out_of_stock == 2:
                stock_status = "超过15天"
                
            highest_price_str = f"{price_symbol}{highest_price}" if highest_price is not None else "未知"
            lowest_price_str = f"{price_symbol}{lowest_price}" if lowest_price is not None else "未知"
            
            print(f"ASIN: {asin} | 历史最高价: {highest_price_str} | 断货状态: {stock_status}")
            
            return result
            
        except Exception as e:
            if self.verbose:
                print(f"爬取Keepa信息时出错: {str(e)}")
            # 即使出错也输出一个简易摘要
            print(f"ASIN: {asin} | 处理出错: {str(e)[:50]}...")
            return result

    def process_asin_batch(self, asins, all_results):
        """
        处理一批ASIN并更新结果
        :param asins: 要处理的ASIN列表
        :param all_results: 共享的结果字典
        """
        batch_results = {}
        
        try:
            for i, asin in enumerate(asins):
                # 检查是否要求停止
                if self.stop_requested:
                    print(f"线程 {threading.current_thread().name} 收到停止请求，中断处理")
                    break
                
                # 处理开始的输出 - 不再受verbose控制
                print(f"开始处理 [{i+1}/{len(asins)}] ASIN: {asin}")
                
                result = self.get_keepa_info_with_browser(asin)
                batch_results[asin] = result
                
                # 每处理完5个ASIN保存一次临时结果到共享结果字典
                if (i + 1) % 5 == 0 or i == len(asins) - 1:
                    with self.results_lock:
                        all_results.update(batch_results)
                        # 实时更新Excel文件
                        self.update_excel_with_historical_data(all_results)
                        # 保存进度检查点
                        self.save_checkpoint(asin)
                    
                # 休息一下避免请求过快
                if i < len(asins) - 1 and not self.stop_requested:
                    sleep_time = 2 + random.random() * 3  # 2-5秒随机时间
                    print(f"休息 {sleep_time:.1f} 秒后继续...")
                    time.sleep(sleep_time)
        finally:
            # 确保关闭浏览器
            self.close_thread_driver()

    def save_checkpoint(self, last_processed_asin):
        """保存处理进度检查点"""
        try:
            checkpoint_data = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "marketplace": self.marketplace,
                "last_processed_asin": last_processed_asin
            }
            
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                
            print(f"已保存进度检查点: {last_processed_asin}")
        except Exception as e:
            print(f"保存检查点时出错: {str(e)}")

    def load_checkpoint(self):
        """加载上次的处理进度"""
        try:
            if not os.path.exists(self.checkpoint_file):
                print("没有找到检查点文件，将从头开始处理")
                return None
                
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
                
            print(f"已加载检查点: {checkpoint_data}")
            return checkpoint_data
        except Exception as e:
            print(f"加载检查点时出错: {str(e)}")
            return None

    def get_remaining_asins(self, all_asins):
        """获取尚未处理的ASIN列表"""
        checkpoint = self.load_checkpoint()
        if not checkpoint:
            return all_asins
            
        if checkpoint.get("marketplace") != self.marketplace:
            print(f"检查点市场({checkpoint.get('marketplace')})与当前市场({self.marketplace})不一致，将从头开始处理")
            return all_asins
            
        last_asin = checkpoint.get("last_processed_asin")
        if not last_asin or last_asin not in all_asins:
            return all_asins
            
        last_index = all_asins.index(last_asin)
        remaining = all_asins[last_index + 1:]
        
        print(f"将从检查点继续，跳过 {last_index + 1} 个已处理的ASIN，剩余 {len(remaining)} 个待处理")
        return remaining

    def process_all_asins(self):
        """多线程处理所有ASIN，获取历史价格信息"""
        print(f"开始使用 {self.threads} 个线程处理所有ASIN，获取历史价格信息...")
        print(f"当前使用的市场: {self.marketplace}")
        
        # 设置运行状态
        self.running = True
        self.stop_requested = False
        
        # 从Excel加载ASIN
        all_asins = self.load_asins_from_excel()
        if not all_asins:
            print("没有有效的ASIN可处理")
            self.running = False
            return False
        
        # 获取剩余待处理的ASIN
        asins = self.get_remaining_asins(all_asins)
        if not asins:
            print("所有ASIN已处理完毕")
            self.running = False
            return True
        
        # 共享的结果字典
        all_results = {}
        
        # 将ASIN列表分成多个批次
        batch_size = max(1, len(asins) // self.threads + (1 if len(asins) % self.threads else 0))
        asin_batches = [asins[i:i+batch_size] for i in range(0, len(asins), batch_size)]
        
        if self.verbose:
            print(f"创建 {len(asin_batches)} 个批次，每批次约 {batch_size} 个ASIN")
        else:
            print(f"处理 {len(asins)} 个ASIN，已分为 {len(asin_batches)} 个批次")
        
        # 使用线程池同时处理多个批次
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 提交任务
            futures = [executor.submit(self.process_asin_batch, batch, all_results) for batch in asin_batches]
            
            # 等待所有任务完成
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # 获取结果，如果有异常会被抛出
                except Exception as e:
                    print(f"处理批次时出错: {str(e)}")
        
        print("所有ASIN处理完成" if not self.stop_requested else "处理被用户中断")
        
        # 最后一次更新Excel
        self.update_excel_with_historical_data(all_results)
        
        # 更新运行状态
        self.running = False
        self.stop_requested = False
        
        return not self.stop_requested

    def request_stop(self):
        """请求停止处理"""
        print("收到停止处理请求，将在完成当前ASIN后停止...")
        self.stop_requested = True

    def update_excel_with_historical_data(self, results):
        """更新Excel文件，添加历史最高价和断货信息"""
        try:
            if self.verbose:
                print(f"更新Excel文件 {self.excel_file} 添加历史价格数据...")
            
            # 读取原始Excel文件
            df = pd.read_excel(self.excel_file)
            
            # 确保有必要的列
            if '历史最高价' not in df.columns:
                df['历史最高价'] = None
            if '历史最低价' not in df.columns:
                df['历史最低价'] = None
            if '断货状态' not in df.columns:
                df['断货状态'] = None
            if '价格符号' not in df.columns:
                df['价格符号'] = None
            
            # 设置价格符号
            price_symbol = self.currency_symbols.get(self.marketplace, "$")
            
            # 查找正确的ASIN列名（可能是任何大小写形式）
            asin_col = None
            for col in df.columns:
                if col.upper() == 'ASIN':
                    asin_col = col
                    break
            
            # 如果没有找到标准ASIN列，尝试其他可能的变体
            if not asin_col:
                possible_variants = ['ASIN', 'asin', 'Asin', 'ASin', 'ProductID', '产品ID']
                for variant in possible_variants:
                    if variant in df.columns:
                        asin_col = variant
                        break
            
            # 如果仍然找不到ASIN列，则报错并返回
            if not asin_col:
                print("错误: 在Excel文件中找不到ASIN列，无法更新数据")
                return False
            
            # 更新数据
            for asin, data in results.items():
                # 确保比较时不区分大小写
                mask = df[asin_col].astype(str).str.upper() == asin.upper()
                if mask.any():
                    df.loc[mask, '历史最高价'] = data.get('historical_highest_price')
                    df.loc[mask, '历史最低价'] = data.get('historical_lowest_price')
                    df.loc[mask, '价格符号'] = price_symbol
                    
                    # 断货状态: None-未知, 1-15天内断货, 2-超过15天断货
                    out_of_stock_status = data.get('out_of_stock_within_month')
                    if out_of_stock_status == 1:
                        df.loc[mask, '断货状态'] = '15天内'
                    elif out_of_stock_status == 2:
                        df.loc[mask, '断货状态'] = '超过15天'
                    else:
                        df.loc[mask, '断货状态'] = '未知'
            
            # 保存到新文件
            df.to_excel(self.output_file, index=False)
            if self.verbose:
                print(f"历史价格数据已保存到 {self.output_file}")
            return True
        except Exception as e:
            print(f"更新Excel文件时出错: {str(e)}")
            return False

class AmazonKeepaPriceApp:
    def __init__(self, root):
        self.root = root
        
        # 使用统一UI主题设置窗口
        AmazonUITheme.setup_window(
            self.root,
            "亚马逊历史价格查询工具", 
            size="950x650", 
            resizable=(True, True)
        )
        
        # 初始化变量
        self.file_path = tk.StringVar()
        self.thread_count = tk.IntVar(value=1)  # 默认为1个线程
        self.checker = None
        self.running = False
        self.marketplace = tk.StringVar(value="US")
        self.verbose = tk.BooleanVar(value=False)  # 默认不显示详细日志
        
        # 创建界面
        self.show_main_interface()
        
        # 重定向stdout到文本控件
        self.redirect_stdout()
        
    def show_main_interface(self):
        # 创建标题栏
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
        
        title_label = ttk.Label(
            title_frame, 
            text="亚马逊历史价格查询工具",
            font=("微软雅黑", 16, "bold"),
            foreground=AmazonUITheme.COLORS["primary"]
        )
        title_label.pack(side=tk.LEFT)
        
        # 添加横向分隔线
        separator = ttk.Separator(self.root, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=15, pady=5)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # 创建左右分栏
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 10), pady=0)
        
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # ===== 左侧配置区域 =====
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(left_frame, text="数据文件", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件路径显示
        file_path_entry = ttk.Entry(file_frame, textvariable=self.file_path, width=30)
        file_path_entry.pack(fill=tk.X, pady=5)
        
        # 浏览按钮
        browse_button = ttk.Button(
            file_frame, 
            text="选择Excel文件", 
            command=self.browse_file,
            width=20
        )
        browse_button.pack(pady=5)
        
        # 市场选择框架
        market_frame = ttk.LabelFrame(left_frame, text="市场设置", padding=10)
        market_frame.pack(fill=tk.X, pady=10)
        
        # 市场选择
        ttk.Label(market_frame, text="选择市场:").pack(anchor=tk.W, pady=(0, 5))
        
        self.market_names = {
            "US": "美国",
            "CA": "加拿大",
            "JP": "日本",
            "UK": "英国",
            "MX": "墨西哥"
        }
        
        # 创建单选按钮组
        for code, name in self.market_names.items():
            ttk.Radiobutton(
                market_frame, 
                text=name, 
                variable=self.marketplace, 
                value=code
            ).pack(anchor=tk.W, pady=2)
        
        # ===== 右侧日志区域 =====
        
        log_frame = ttk.LabelFrame(right_frame, text="运行日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框与滚动条
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, bg="#fafafa", font=("微软雅黑", 9))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # ===== 底部按钮区域 =====
        
        # 先添加横向分隔线
        separator2 = ttk.Separator(self.root, orient=tk.HORIZONTAL)
        separator2.pack(fill=tk.X, padx=15, pady=5)
        
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=15, pady=(5, 15))
        
        # 按钮样式
        style = ttk.Style()
        style.configure(
            "Accent.TButton", 
            background=AmazonUITheme.COLORS["accent"],
            foreground="white"
        )
        
        # 开始按钮
        self.start_button = ttk.Button(
            button_frame, 
            text="开始查询", 
            command=self.start_process,
            style="Accent.TButton",
            width=20
        )
        self.start_button.pack(side=tk.RIGHT, padx=5)
        
        # 继续按钮
        self.continue_button = ttk.Button(
            button_frame, 
            text="继续上次查询", 
            command=self.continue_process,
            width=20
        )
        self.continue_button.pack(side=tk.RIGHT, padx=5)
        
        # 停止按钮
        self.stop_button = ttk.Button(
            button_frame, 
            text="停止查询", 
            command=self.stop_process,
            width=20
        )
        self.stop_button.pack(side=tk.RIGHT, padx=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # 打开结果文件按钮
        def open_output_file():
            if self.checker and os.path.exists(self.checker.output_file):
                if platform.system() == "Windows":
                    os.startfile(self.checker.output_file)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.checker.output_file])
                else:  # Linux
                    subprocess.run(["xdg-open", self.checker.output_file])
            else:
                messagebox.showinfo("提示", "结果文件不存在，请先运行查询")
        
        self.open_file_button = ttk.Button(
            button_frame, 
            text="打开结果文件", 
            command=open_output_file,
            width=20
        )
        self.open_file_button.pack(side=tk.LEFT, padx=5)
        
        # 添加状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=5)
        
        self.status_label = ttk.Label(
            status_frame, 
            text="准备就绪",
            font=("微软雅黑", 9),
            foreground="#666666"
        )
        self.status_label.pack(side=tk.LEFT)

    def redirect_stdout(self):
        class TextRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget
                self.buffer = ""
            
            def write(self, string):
                self.buffer += string
                self.text_widget.insert(tk.END, string)
                self.text_widget.see(tk.END)
            
            def flush(self):
                pass
        
        self.stdout_redirector = TextRedirector(self.log_text)
        sys.stdout = self.stdout_redirector
        
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择文件",
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.file_path.set(file_path)
    
    def toggle_buttons(self, running=False):
        """根据运行状态切换按钮状态"""
        if running:
            self.start_button.config(state="disabled")
            self.continue_button.config(state="disabled")
            self.stop_button.config(state="normal")
        else:
            self.start_button.config(state="normal")
            self.continue_button.config(state="normal")
            self.stop_button.config(state="disabled")
            
    def start_process(self):
        # 获取当前设置值
        self.excel_file = self.file_path.get().strip()
        
        if not self.excel_file:
            messagebox.showerror("错误", "请选择Excel文件")
            return
            
        if not os.path.exists(self.excel_file):
            messagebox.showerror("错误", f"文件不存在: {self.excel_file}")
            return
        
        # 确保扩展名是.xlsx
        if not self.excel_file.lower().endswith('.xlsx'):
            messagebox.showerror("错误", "请选择.xlsx格式的Excel文件")
            return
            
        marketplace = self.marketplace.get()
        verbose = self.verbose.get()
        
        # 显示设置信息
        print(f"开始处理 Excel文件: {self.excel_file}")
        print(f"市场: {marketplace} ({self.market_names.get(marketplace, '未知')})")
        
        # 禁用开始按钮，启用停止按钮
        self.toggle_buttons(running=True)
        
        # 创建并启动工作线程
        self.checker = KeepaHistoricalPriceChecker(
            threads=self.thread_count.get(),
            marketplace=marketplace,
            excel_file=self.excel_file,
            verbose=verbose
        )
        
        self.process_thread = threading.Thread(target=self.run_process)
        self.process_thread.daemon = True
        self.process_thread.start()
    
    def continue_process(self):
        # 获取用户设置
        file_path = self.file_path.get()
        market_str = "JP"  # 固定为日本市场
        
        if not file_path:
            self.log_text.insert(tk.END, "请选择包含ASIN的文件\n")
            return
            
        if not os.path.exists(file_path):
            self.log_text.insert(tk.END, f"无法找到文件: {file_path}\n")
            return
            
        # 创建检查器实例以获取正确的检查点文件路径
        self.checker = KeepaHistoricalPriceChecker(
            threads=1,  # 固定线程数
            marketplace=market_str,
            excel_file=file_path,
            verbose=False  # 设置为False简化输出
        )
            
        # 检查检查点文件
        checkpoint_file = self.checker.checkpoint_file
        if not os.path.exists(checkpoint_file):
            self.log_text.insert(tk.END, "没有找到处理检查点，将从头开始处理\n")
        
        # 设置运行状态和按钮
        self.running = True
        self.toggle_buttons(running=True)
        
        # 保留日志区域内容，添加分隔线
        self.log_text.insert(tk.END, "\n" + "-"*80 + "\n继续处理...\n" + "-"*80 + "\n")
        
        # 在新线程中执行处理
        threading.Thread(target=self.run_process, daemon=True).start()
    
    def stop_process(self):
        """请求停止处理"""
        if self.checker and self.running:
            self.checker.request_stop()
            print("已发送停止请求，将在完成当前任务后停止...")
            
            # 禁用停止按钮防止重复点击
            self.stop_button.config(state="disabled")
    
    def run_process(self):
        try:
            print(f"开始处理文件: {self.checker.excel_file}")
            print(f"选择的市场: {self.checker.marketplace}")
            
            success = self.checker.process_all_asins()
            
            # 处理完成后重新启用按钮
            self.running = False
            self.root.after(0, lambda: self.toggle_buttons(running=False))
            
            if success:
                print(f"处理完成! 结果已保存至: {self.checker.output_file}")
            else:
                print("处理过程中发生错误或被用户中断，请检查日志")
                
        except Exception as e:
            print(f"处理过程中发生错误: {str(e)}")
            self.running = False
            self.root.after(0, lambda: self.toggle_buttons(running=False))
            print(f"处理过程中发生错误: {str(e)}")

def main():
    # 禁用原生selenium日志
    logging.getLogger('selenium').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # 创建并运行GUI
    root = tk.Tk()
    
    # 设置应用程序图标和任务栏图标
    try:
        # 先设置应用程序ID - 这会帮助Windows正确识别应用并更新任务栏图标
        if platform.system() == "Windows":
            try:
                import ctypes
                app_id = "AmazonKeepaPriceApp.App.1.0"  # 唯一应用ID
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
            except Exception:
                pass
        
        # 设置窗口图标
        icon_path = "icon.ico"
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
            
            # 在Windows上使用多种方法设置任务栏图标
            if platform.system() == "Windows":
                try:
                    import ctypes
                    # 获取窗口根句柄 - 更可靠的方法
                    hwnd = ctypes.windll.user32.GetForegroundWindow()
                    
                    # 常量定义
                    ICON_SMALL = 0
                    ICON_BIG = 1
                    WM_SETICON = 0x0080
                    LR_LOADFROMFILE = 0x0010
                    IMAGE_ICON = 1
                    
                    # 使用绝对路径
                    abs_icon_path = os.path.abspath(icon_path)
                    
                    # 加载小图标
                    h_icon_small = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 16, 16, LR_LOADFROMFILE
                    )
                    if h_icon_small:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                    
                    # 加载大图标
                    h_icon_big = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 32, 32, LR_LOADFROMFILE
                    )
                    if h_icon_big:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                    
                    # 刷新窗口以应用更改
                    ctypes.windll.user32.UpdateWindow(hwnd)
                    
                    # 在窗口显示后再次设置图标 - 使用事件回调
                    def set_icon_after_visible():
                        try:
                            hwnd = ctypes.windll.user32.GetForegroundWindow()
                            if h_icon_small:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                            if h_icon_big:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                        except Exception:
                            pass
                    
                    # 在100ms后执行以确保窗口已完全加载
                    root.after(100, set_icon_after_visible)
                except Exception:
                    pass
    except:
        pass  # 忽略图标设置错误
        
    app = AmazonKeepaPriceApp(root)
    
    # 窗口关闭确认
    def on_closing():
        if app.running:
            if messagebox.askyesno("确认", "处理正在进行中，确定要退出吗？"):
                if app.checker:
                    app.checker.request_stop()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 开始主循环
    root.mainloop()
    
    # 恢复标准输出
    if hasattr(app, 'old_stdout'):
        sys.stdout = app.old_stdout

if __name__ == "__main__":
    main() 