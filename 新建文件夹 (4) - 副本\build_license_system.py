import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
import re
from encrypt_files import encrypt_file, decrypt_file

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("BuildScript")

# 定义文件路径
CLIENT_PY = "license_client.py"
COLLECTOR_PY = "采集8.py"
FILTER_PY = "筛品终极版1.py"
PRICE_PY = "历史价格7.py"
PATENT_PY = "专利1.py"  # 新增专利文件
SERVER_PY = "license_server.py"
UI_THEME_PY = "ui_theme.py"
ICON_FILE = "icon.ico"

# 加密密钥
ENCRYPT_KEY = "AmazonLicenseSecretKey2023"

def check_cryptography_installed():
    """检查是否安装了cryptography库"""
    try:
        import cryptography
        logger.info(f"检测到cryptography库版本: {cryptography.__version__}")
        return True
    except ImportError:
        logger.warning("未检测到cryptography库，尝试安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "cryptography"])
            logger.info("cryptography库安装成功")
            return True
        except Exception as e:
            logger.error(f"安装cryptography库失败: {str(e)}")
            logger.warning("将使用基础XOR加密")
            return False

def embed_ui_theme(source_file, theme_file, output_file=None):
    """将UI主题代码嵌入到脚本中，替代import语句"""
    logger.info(f"将UI主题嵌入到 {source_file}")
    
    if output_file is None:
        output_file = source_file
    
    # 读取UI主题文件内容
    with open(theme_file, 'r', encoding='utf-8') as f:
        theme_content = f.read()
    
    # 读取源文件内容
    with open(source_file, 'r', encoding='utf-8') as f:
        source_content = f.read()
    
    # 替换import语句部分，保留现有的备用AmazonUITheme实现
    import_pattern = r'try:\s*from ui_theme import AmazonUITheme\s*except ImportError:\s*# 如果找不到主题模块，定义简化版的AmazonUITheme\s*class AmazonUITheme:'
    
    theme_class_def = 'class AmazonUITheme:'
    theme_class_content = theme_content.split(theme_class_def)[1]
    
    replacement = f"""# UI主题内嵌 - 从ui_theme.py生成
class AmazonUITheme:"""
    
    modified_content = re.sub(import_pattern, replacement, source_content, flags=re.DOTALL)
    
    # 如果没有找到import语句模式，就找AmazonUITheme类定义并替换
    if modified_content == source_content:
        class_pattern = r'class AmazonUITheme:.*?def get_image_path\(cls\):.*?return None'
        modified_content = re.sub(class_pattern, f"class AmazonUITheme:{theme_class_content}", source_content, flags=re.DOTALL)
    
    # 写入修改后的内容到输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    logger.info(f"UI主题嵌入完成: {output_file}")
    return output_file

def update_license_client():
    """更新license_client.py中的文件名和依赖检查功能"""
    logger.info("更新license_client.py")
    
    with open(CLIENT_PY, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新可用程序列表，确保文件名为加密后的文件名
    updated_content = content.replace(
        'self.available_programs = {\n            "采集": "采集8.py",\n            "筛品": "筛品终极版1.py",\n            "价格": "历史价格7.py"\n        }',
        'self.available_programs = {\n            "采集": "采集8.py.encrypted",\n            "筛品": "筛品终极版1.py.encrypted",\n            "价格": "历史价格7.py.encrypted"\n        }'
    )
    
    # 确保包含cryptography库依赖
    if 'cryptography' not in updated_content:
        # 更新依赖包字典
        dependency_pattern = r"required_packages = \{\s*'selenium': 'selenium',.*?'amazoncaptcha': 'amazoncaptcha'\s*\}"
        dependency_replacement = "required_packages = {\n                'selenium': 'selenium',\n                'beautifulsoup4': 'bs4',\n                'pandas': 'pandas',\n                'requests': 'requests',\n                'webdriver_manager': 'webdriver_manager',\n                'psutil': 'psutil',\n                'amazoncaptcha': 'amazoncaptcha',\n                'cryptography': 'cryptography',  # 加密库依赖\n                'fake_useragent': 'fake_useragent'  # 添加用户代理库依赖\n            }"
        updated_content = re.sub(dependency_pattern, dependency_replacement, updated_content, flags=re.DOTALL)
    
    # 确保有加密库导入
    if 'from cryptography' not in updated_content:
        import_pattern = r"import uuid\n"
        crypto_imports = """import uuid
import secrets  # 用于安全随机数生成

# 尝试导入cryptography相关库
try:
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.backends import default_backend
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.fernet import Fernet
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
"""
        updated_content = updated_content.replace(import_pattern, crypto_imports)
    
    # 添加更强大的依赖检查代码（如果还没有）
    if 'def check_and_install_dependencies(self, required_packages=None, force_check=False):' not in updated_content:
        dependency_check_code = '''
    def check_and_install_dependencies(self, required_packages=None, force_check=False):
        """检查并安装程序所需的依赖库，使用缓存机制减少检查频率"""
        # 初始化默认依赖包
        if required_packages is None:
            required_packages = {
                'selenium': 'selenium',
                'beautifulsoup4': 'bs4',
                'pandas': 'pandas',
                'requests': 'requests',
                'webdriver_manager': 'webdriver_manager',
                'psutil': 'psutil',
                'amazoncaptcha': 'amazoncaptcha',
                'cryptography': 'cryptography',  # 加密库依赖
                'fake_useragent': 'fake_useragent'  # 用户代理库依赖
            }
        
        # 如果不是强制检查，且有有效的依赖缓存，则跳过检查
        current_time = datetime.now()
        
        if not force_check and self.dependencies_cache and 'last_check' in self.dependencies_cache:
            # 获取上次检查时间和缓存的依赖状态
            last_check = datetime.fromisoformat(self.dependencies_cache['last_check'])
            cached_status = self.dependencies_cache.get('status', {})
            
            # 检查所有需要的包是否都已缓存为已安装状态
            all_installed = True
            for package_name in required_packages:
                if package_name not in cached_status or not cached_status[package_name]:
                    all_installed = False
                    break
            
            # 如果上次检查时间在7天内，且所有包都已安装，直接返回成功
            if all_installed and (current_time - last_check) < timedelta(days=7):
                logger.info("使用缓存的依赖检查结果，跳过依赖检查")
                return True
        
        # 开始检查依赖
        missing_packages = []
        packages_status = {}
        
        for package_name, import_name in required_packages.items():
            try:
                import importlib
                importlib.import_module(import_name)
                packages_status[package_name] = True
            except ImportError:
                missing_packages.append(package_name)
                packages_status[package_name] = False
        
        # 安装缺失的包
        if missing_packages:
            try:
                import subprocess
                import sys
                
                self.silent_log(f"需要安装的依赖包: {', '.join(missing_packages)}")
                
                for package in missing_packages:
                    self.silent_log(f"正在安装 {package}...")
                    subprocess.check_call(
                        [sys.executable, "-m", "pip", "install", package],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    # 安装成功后更新状态
                    packages_status[package] = True
                
                self.silent_log("所有依赖包安装完成")
            except Exception as e:
                self.silent_log(f"安装依赖包时出错: {str(e)}")
                # 注意：不更新缓存，因为安装失败
                return False
        
        # 更新依赖缓存
        self.dependencies_cache = {
            'last_check': current_time.isoformat(),
            'status': packages_status
        }
        
        # 保存缓存到文件
        self.save_dependencies_cache()
        
        return True
'''
        # 在类定义后添加依赖检查函数
        updated_content = updated_content.replace(
            'class SimpleClient:\n    def __init__(self):',
            f'class SimpleClient:\n{dependency_check_code}\n    def __init__(self):'
        )
    
    # 在run_program方法中添加依赖检查调用
    if 'self.check_and_install_dependencies()' not in updated_content:
        updated_content = updated_content.replace(
            '    def run_program(self, program_type):\n        """运行指定类型的程序"""',
            '    def run_program(self, program_type):\n        """运行指定类型的程序"""\n        # 检查并安装所需依赖\n        self.check_and_install_dependencies()'
        )
    
    # 更新文件
    with open(CLIENT_PY, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    logger.info("license_client.py 更新完成")

def update_license_server():
    """更新license_server.py中的文件路径和下载逻辑"""
    logger.info("更新license_server.py")
    
    with open(SERVER_PY, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新程序路径，使用加密文件
    updated_content = content.replace(
        'PROGRAM_PATHS = {\n    "价格": "/opt/license_manager/历史价格7.py",\n    "历史价格": "/opt/license_manager/历史价格7.py",  # 添加别名支持\n    "采集": "/opt/license_manager/采集8.py",\n    "筛品": "/opt/license_manager/筛品终极版1.py"\n}',
        'PROGRAM_PATHS = {\n    "价格": "/opt/license_manager/历史价格7.py.encrypted",\n    "历史价格": "/opt/license_manager/历史价格7.py.encrypted",  # 添加别名支持\n    "采集": "/opt/license_manager/采集8.py.encrypted",\n    "筛品": "/opt/license_manager/筛品终极版1.py.encrypted"\n}'
    )
    
    # 添加请求签名验证（如果服务器支持）
    if 'def check_signature' not in updated_content:
        before_pattern = '@app.route("/license/check", methods=["POST"])'
        signature_code = '''
# 签名验证函数
def check_signature(data):
    """验证请求签名"""
    # 验证所需字段
    required_fields = ["key", "device_id", "timestamp", "nonce", "signature"]
    for field in required_fields:
        if field not in data:
            return False, f"缺少必要字段: {field}"
    
    # 验证时间戳（防止重放攻击）
    try:
        timestamp = int(data["timestamp"])
        current_time = int(time.time())
        # 允许5分钟时间差
        if abs(current_time - timestamp) > 300:
            return False, "请求已过期，请检查系统时间"
    except:
        return False, "无效的时间戳"
    
    # 构建签名数据
    key = data["key"]
    device_id = data["device_id"]
    timestamp = data["timestamp"]
    nonce = data["nonce"]
    signature = data["signature"]
    
    # 计算预期签名
    signature_data = f"{key}:{device_id}:{timestamp}:{nonce}"
    expected_signature = hashlib.sha256(signature_data.encode()).hexdigest()
    
    # 验证签名
    if signature != expected_signature:
        return False, "请求签名无效"
    
    return True, ""

'''
        updated_content = updated_content.replace(before_pattern, signature_code + before_pattern)
    
    # 更新许可证检查函数，添加签名验证
    if 'check_signature(data)' not in updated_content:
        check_route_pattern = r'@app.route\("/license/check", methods=\["POST"\]\)\ndef check_license\(\):\s*data = request.json'
        check_route_replacement = '''@app.route("/license/check", methods=["POST"])
def check_license():
    data = request.json
    
    # 验证请求签名
    if "signature" in data:
        valid_sig, error_msg = check_signature(data)
        if not valid_sig:
            return jsonify({"valid": False, "message": error_msg})
    '''
        updated_content = re.sub(check_route_pattern, check_route_replacement, updated_content, flags=re.DOTALL)
    
    # 在响应中添加服务器签名
    if 'server_signature' not in updated_content:
        response_pattern = r'return jsonify\(\{\s*"valid": True,\s*"message": "许可证有效",\s*"allowed_programs": allowed_programs\s*\}\)'
        response_replacement = '''    # 生成服务器响应签名
    timestamp = data.get("timestamp", "")
    nonce = data.get("nonce", "")
    key = data.get("key", "")
    server_data = f"True:{key}:{timestamp}:{nonce}"
    server_signature = hashlib.sha256(server_data.encode()).hexdigest()
    
    return jsonify({
        "valid": True,
        "message": "许可证有效",
        "allowed_programs": allowed_programs,
        "server_signature": server_signature
    })'''
        updated_content = re.sub(response_pattern, response_replacement, updated_content, flags=re.DOTALL)
    
    # 更新文件
    with open(SERVER_PY, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    logger.info("license_server.py 更新完成")

def encrypt_files():
    """加密Python脚本文件"""
    logger.info("开始加密Python脚本文件")
    
    # 检查是否安装了cryptography库
    has_crypto = check_cryptography_installed()
    if has_crypto:
        logger.info("将使用高级AES加密")
    else:
        logger.warning("将使用基础XOR加密")
    
    files_to_encrypt = [
        COLLECTOR_PY,
        FILTER_PY, 
        PRICE_PY,
        PATENT_PY  # 添加专利文件
    ]
    
    encrypted_files = []
    
    # 先嵌入UI主题
    temp_dir = Path("temp_embed")
    temp_dir.mkdir(exist_ok=True)
    
    for file in files_to_encrypt:
        # 复制到临时目录
        temp_file = temp_dir / file
        shutil.copy2(file, temp_file)
        
        # 嵌入UI主题
        embed_ui_theme(temp_file, UI_THEME_PY)
        
        # 加密文件
        encrypted_file = encrypt_file(temp_file, file + ".encrypted", ENCRYPT_KEY)
        encrypted_files.append(encrypted_file)
        
        logger.info(f"已加密文件: {encrypted_file}")
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    return encrypted_files

def build_client_exe():
    """构建客户端可执行文件"""
    logger.info("开始构建客户端可执行文件")
    
    try:
        # 检查PyInstaller是否已安装
        try:
            import PyInstaller
            logger.info(f"检测到PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            logger.warning("未检测到PyInstaller，尝试安装...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            logger.info("PyInstaller安装完成")
        
        # 准备PyInstaller命令
        pyinstaller_cmd = [
            "pyinstaller",
            "--onefile",                      # 单文件模式
            "--windowed",                     # 无控制台窗口
            "--clean",                        # 清理临时文件
            f"--icon={ICON_FILE}",            # 设置图标
            "--name=亚马逊授权客户端",          # 输出文件名
            f"--add-data={COLLECTOR_PY}.encrypted;.",  # 添加加密采集文件
            f"--add-data={FILTER_PY}.encrypted;.",     # 添加加密筛品文件
            f"--add-data={PRICE_PY}.encrypted;.",      # 添加加密价格文件
            f"--add-data={PATENT_PY}.encrypted;.",     # 添加加密专利文件
            f"--add-data={ICON_FILE};.",             # 添加图标文件
            "--hidden-import=cryptography",   # 添加cryptography依赖
            "--hidden-import=fake_useragent", # 添加fake_useragent依赖
            CLIENT_PY                          # 主程序
        ]
        
        logger.info(f"执行PyInstaller命令: {' '.join(pyinstaller_cmd)}")
        
        # 执行PyInstaller命令
        result = subprocess.run(pyinstaller_cmd, check=True, capture_output=True, text=True)
        logger.info("PyInstaller构建成功")
        
        # 检查dist目录
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))
        
        if exe_files:
            logger.info(f"生成的可执行文件: {[str(f) for f in exe_files]}")
            return True
        else:
            logger.error("构建成功但未找到exe文件")
            return False
    
    except Exception as e:
        logger.error(f"构建可执行文件时出错: {str(e)}")
        if hasattr(e, 'stderr'):
            logger.error(f"错误详情: {e.stderr}")
        return False

def prepare_server_files():
    """准备服务器端文件"""
    logger.info("准备服务器端文件")
    
    server_dir = Path("server_files")
    server_dir.mkdir(exist_ok=True)
    
    # 复制服务器脚本
    server_file = server_dir / SERVER_PY
    shutil.copy2(SERVER_PY, server_file)
    
    # 复制加密的Python脚本
    encrypted_files = [
        f"{COLLECTOR_PY}.encrypted",
        f"{FILTER_PY}.encrypted", 
        f"{PRICE_PY}.encrypted",
        f"{PATENT_PY}.encrypted"  # 添加专利文件
    ]
    
    for file in encrypted_files:
        if os.path.exists(file):
            dest = server_dir / file
            shutil.copy2(file, dest)
            logger.info(f"已复制到服务器目录: {dest}")
        else:
            logger.error(f"找不到加密文件: {file}")
    
    # 复制图标文件
    icon_dest = server_dir / ICON_FILE
    shutil.copy2(ICON_FILE, icon_dest)
    logger.info(f"已复制图标到服务器目录: {icon_dest}")
    
    logger.info(f"服务器文件准备完成，位于目录: {server_dir}")
    return True

def cleanup():
    """清理临时文件"""
    logger.info("清理临时文件")
    
    # 清理PyInstaller生成的临时目录
    dirs_to_clean = ['build', '__pycache__', 'temp_embed']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            shutil.rmtree(dir_name)
            logger.info(f"已删除目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        os.remove(spec_file)
        logger.info(f"已删除spec文件: {spec_file}")
    
    # 清理日志文件
    #for log_file in Path('.').glob('*.log'):
    #    os.remove(log_file)
    #    logger.info(f"已删除日志文件: {log_file}")
    
    return True

def test_decrypt():
    """测试解密功能是否正常工作"""
    logger.info("测试解密功能")
    
    test_file = f"{COLLECTOR_PY}.encrypted"
    try:
        temp_decrypted = f"{COLLECTOR_PY}.test_decrypted"
        decrypt_file(test_file, temp_decrypted, ENCRYPT_KEY)
        
        logger.info(f"测试解密成功: {temp_decrypted}")
        
        # 清理测试文件
        if os.path.exists(temp_decrypted):
            os.remove(temp_decrypted)
        
        return True
        
    except Exception as e:
        logger.error(f"测试解密失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始构建授权系统")
    
    try:
        # 0. 检查加密库
        logger.info("步骤0: 检查加密库")
        has_crypto = check_cryptography_installed()
        
        # 1. 更新license_client.py
        logger.info("步骤1: 更新license_client.py")
        update_license_client()
        
        # 2. 更新license_server.py
        logger.info("步骤2: 更新license_server.py")
        update_license_server()
        
        # 3. 加密Python脚本
        logger.info("步骤3: 加密Python脚本")
        encrypt_files()
        
        # 4. 测试解密功能
        logger.info("步骤4: 测试解密功能")
        if not test_decrypt():
            logger.error("解密测试失败，终止构建")
            return False
        
        # 5. 构建客户端可执行文件
        logger.info("步骤5: 构建客户端可执行文件")
        if not build_client_exe():
            logger.error("构建客户端可执行文件失败，终止构建")
            return False
        
        # 6. 准备服务器文件
        logger.info("步骤6: 准备服务器文件")
        if not prepare_server_files():
            logger.error("准备服务器文件失败")
            # 继续执行，因为这不是致命错误
        
        # 7. 清理临时文件
        logger.info("步骤7: 清理临时文件")
        if not cleanup():
            logger.warning("清理临时文件失败")
            # 继续执行，因为这不是致命错误
        
        logger.info("构建过程完成")
        logger.info("请将dist目录中的可执行文件分发给用户")
        logger.info("请将server_files目录中的文件上传到服务器")
        
        return True
        
    except Exception as e:
        logger.error(f"构建过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 