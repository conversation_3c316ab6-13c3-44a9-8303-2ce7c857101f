from flask import Flask, request, jsonify, send_file
from datetime import datetime, timedelta
import json
import os
import hashlib
import time
from flask_cors import CORS
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/license_server.log'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 存储激活码信息的数据库文件
LICENSE_DB = "/opt/license_manager/license_database.json"
# 程序文件存放位置
PROGRAM_PATHS = {
    "价格": "/opt/license_manager/历史价格7.py.encrypted",
    "历史价格": "/opt/license_manager/历史价格7.py.encrypted",  # 添加别名支持
    "采集": "/opt/license_manager/采集8.py.encrypted",
    "筛品": "/opt/license_manager/筛品终极版1.py.encrypted",
    "专利": "/opt/license_manager/专利1.py.encrypted"
}
# 图标文件存放位置
ICON_PATH = "/opt/license_manager/icon.ico"
# 欧陆扩展路径 - 修改为指向zip压缩文件
EXTENSION_PATH = "/opt/license_manager/oalur-extension-V1.9.3_3.zip"

# 权限级别定义
PERMISSION_LEVELS = {
    1: ["采集"],
    2: ["采集", "筛品"],
    3: ["采集", "筛品", "价格", "历史价格"],
    4: ["采集", "筛品", "价格", "历史价格", "专利"]
}

# 初始化数据库
def init_database():
    if not os.path.exists(LICENSE_DB):
        with open(LICENSE_DB, 'w') as f:
            json.dump({}, f)
        logging.info("Created new license database")
    
    # 确保图标文件存在
    if not os.path.exists(ICON_PATH):
        logging.warning(f"Icon file not found at {ICON_PATH}")

# 加载数据库
def load_database():
    try:
        with open(LICENSE_DB, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Error loading license database: {str(e)}")
        return {}

# 保存数据库
def save_database(data):
    try:
        with open(LICENSE_DB, 'w') as f:
            json.dump(data, f, indent=4)
    except Exception as e:
        logging.error(f"Error saving license database: {str(e)}")

# 生成激活码
def generate_key(expire_days=30, permission_level=1):
    import random
    import string
    
    # 生成随机ID
    random_id = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
    
    # 计算过期日期
    expire_date = (datetime.now() + timedelta(days=expire_days)).strftime("%Y%m%d")
    
    # 生成验证码
    secret_key = "T0P$ECRET-K3Y-F0R-V3RIF1C4TI0N"
    verify_code = hashlib.sha256((random_id + expire_date + secret_key).encode()).hexdigest()[:8]
    
    # 组合成完整的激活码
    key = f"{random_id}-{expire_date}-{verify_code}"
    return key

# 检查日期是否为今天，用于重置日期计数器
def is_today(date_str):
    """检查日期字符串是否为今天"""
    try:
        today = datetime.now().strftime("%Y-%m-%d")
        return date_str.startswith(today)
    except:
        return False

# 重置今日使用计数
def reset_daily_usage_if_needed(license_info):
    """如果上次使用日期不是今天，重置今日使用计数"""
    today = datetime.now().strftime("%Y-%m-%d")
    last_used_date = license_info.get('last_used_date', '')
    
    if not last_used_date.startswith(today):
        license_info['today_usage_count'] = 0
        license_info['last_used_date'] = today
        
    return license_info

# 签名验证函数
def check_signature(data):
    """验证请求签名"""
    # 验证所需字段
    required_fields = ["key", "device_id", "timestamp", "nonce", "signature"]
    for field in required_fields:
        if field not in data:
            return False, f"缺少必要字段: {field}"
    
    # 验证时间戳（防止重放攻击）
    try:
        timestamp = int(data["timestamp"])
        current_time = int(time.time())
        # 允许5分钟时间差
        if abs(current_time - timestamp) > 300:
            return False, "请求已过期，请检查系统时间"
    except:
        return False, "无效的时间戳"
    
    # 构建签名数据
    key = data["key"]
    device_id = data["device_id"]
    timestamp = data["timestamp"]
    nonce = data["nonce"]
    signature = data["signature"]
    
    # 计算预期签名
    signature_data = f"{key}:{device_id}:{timestamp}:{nonce}"
    expected_signature = hashlib.sha256(signature_data.encode()).hexdigest()
    
    # 验证签名
    if signature != expected_signature:
        return False, "请求签名无效"
    
    return True, ""

# 验证激活码
@app.route('/license/check', methods=['POST'])
def check_license():
    try:
        data = request.json
        
        # 验证请求签名
        if "signature" in data:
            valid_sig, error_msg = check_signature(data)
            if not valid_sig:
                logging.warning(f"签名验证失败: {error_msg}, 来自设备: {data.get('device_id', 'unknown')}")
                return jsonify({
                    "valid": False,
                    "message": error_msg
                })
        
        key = data.get('key')
        device_id = data.get('device_id')
        
        if not key or not device_id:
            return jsonify({
                "valid": False,
                "message": "缺少必要参数"
            })
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "valid": False,
                "message": "激活码不存在"
            })
        
        license_info = db[key]
        
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return jsonify({
                "valid": False,
                "message": "激活码已过期"
            })
        
        # 检查设备绑定
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return jsonify({
                "valid": False,
                "message": "激活码已绑定到其他设备"
            })
        
        # 重置今日使用计数（如果是新的一天）
        license_info = reset_daily_usage_if_needed(license_info)
        
        # 检查今日使用次数是否超过限制（默认20次）
        today_usage_count = license_info.get('today_usage_count', 0)
        daily_limit = 20  # 每日使用次数限制
        
        if today_usage_count >= daily_limit:
            logging.warning(f"激活码 {key} 今日使用次数已达上限 ({today_usage_count}/{daily_limit})")
            return jsonify({
                "valid": False,
                "message": f"今日使用次数已达上限 ({today_usage_count}/{daily_limit})"
            })
        
        # 更新激活信息
        if not license_info.get('device_id'):
            license_info['device_id'] = device_id
            license_info['activated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 更新使用信息
        license_info['last_used_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        license_info['last_used_date'] = datetime.now().strftime("%Y-%m-%d")
        license_info['today_usage_count'] = today_usage_count + 1
        license_info['total_usage_count'] = license_info.get('total_usage_count', 0) + 1
        
        # 保存更新
        db[key] = license_info
        save_database(db)
        
        # 返回授权信息
        remaining_days = (expire_date - datetime.now()).days
        remaining_count = daily_limit - license_info['today_usage_count']
        
        # 返回授权级别信息
        permission_level = license_info.get('permission_level', 1)
        allowed_programs = PERMISSION_LEVELS.get(permission_level, [])
        
        return jsonify({
            "valid": True,
            "message": "激活码有效",
            "license_info": {
                "expire_date": license_info['expire_date'],
                "remaining_days": remaining_days,
                "daily_limit": daily_limit,
                "today_usage_count": license_info['today_usage_count'],
                "remaining_count": remaining_count,
                "permission_level": permission_level,
                "allowed_programs": allowed_programs
            }
        })
    
    except Exception as e:
        logging.error(f"Error in check_license: {str(e)}")
        return jsonify({
            "valid": False,
            "message": "服务器错误，请稍后再试"
        })

# 生成激活码
@app.route('/license/generate', methods=['POST'])
def generate_license():
    try:
        data = request.json
        
        expire_days = int(data.get('expire_days', 30))
        quantity = int(data.get('quantity', 1))
        permission_level = int(data.get('permission_level', 1))
        
        if quantity <= 0 or quantity > 100:
            return jsonify({
                "success": False,
                "message": "生成数量必须在1-100之间"
            })
            
        if expire_days <= 0:
            return jsonify({
                "success": False,
                "message": "有效期必须大于0天"
            })
            
        if permission_level not in PERMISSION_LEVELS:
            return jsonify({
                "success": False,
                "message": f"无效的权限级别: {permission_level}"
            })
        
        # 生成激活码
        keys = []
        db = load_database()
        
        for _ in range(quantity):
            key = generate_key(expire_days, permission_level)
            expire_date = (datetime.now() + timedelta(days=expire_days)).strftime("%Y%m%d")
            
            # 保存到数据库
            db[key] = {
                "key": key,
                "status": "active",
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "expire_date": expire_date,
                "device_id": None,
                "activated_at": None,
                "last_used_time": None,
                "today_usage_count": 0,
                "total_usage_count": 0,
                "permission_level": permission_level
            }
            
            keys.append(key)
        
        # 保存数据库
        save_database(db)
        
        return jsonify({
            "success": True,
            "message": f"成功生成 {quantity} 个激活码",
            "keys": keys
        })
        
    except Exception as e:
        logging.error(f"Error in generate_license: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"生成激活码错误: {str(e)}"
        })

# 修改激活码
@app.route('/license/modify', methods=['POST'])
def modify_license():
    try:
        data = request.json
        key = data.get('key')
        action = data.get('action')
        
        if not key or not action:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            })
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            })
        
        license_info = db[key]
        
        # 根据不同操作进行处理
        if action == "extend":
            # 延长有效期
            days = int(data.get('days', 30))
            if days <= 0:
                return jsonify({
                    "success": False,
                    "message": "延长天数必须大于0"
                })
            
            # 计算新的过期日期
            try:
                expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
            except:
                expire_date = datetime.now()
            
            # 如果已经过期，则从当前日期开始计算
            if expire_date < datetime.now():
                expire_date = datetime.now()
            
            new_expire_date = (expire_date + timedelta(days=days)).strftime("%Y%m%d")
            license_info['expire_date'] = new_expire_date
            
            message = f"激活码有效期延长 {days} 天，新的过期日期为 {new_expire_date}"
        
        elif action == "revoke":
            # 撤销激活码
            license_info['status'] = "revoked"
            message = "激活码已被撤销"
        
        elif action == "reset":
            # 重置设备绑定（扣除1天有效期）
            license_info['device_id'] = None
            
            # 计算新的过期日期
            try:
                expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
                # 如果当前时间已经超过过期时间，则不再扣除
                if expire_date > datetime.now():
                    new_expire_date = (expire_date - timedelta(days=1)).strftime("%Y%m%d")
                    license_info['expire_date'] = new_expire_date
                    message = f"设备绑定已重置，并扣除1天有效期，新的过期日期为 {new_expire_date}"
                else:
                    message = "设备绑定已重置，当前激活码已过期，不再扣除有效期"
            except:
                message = "设备绑定已重置，无法计算新的过期日期"
        
        elif action == "reset_usage":
            # 重置今日使用次数
            license_info['today_usage_count'] = 0
            message = "今日使用次数已重置为0"
        
        elif action == "change_level":
            # 修改权限级别
            permission_level = int(data.get('permission_level', 1))
            if permission_level not in PERMISSION_LEVELS:
                return jsonify({
                    "success": False,
                    "message": f"无效的权限级别: {permission_level}"
                })
            
            license_info['permission_level'] = permission_level
            message = f"权限级别已修改为 {permission_level} 级"
            
        else:
            return jsonify({
                "success": False,
                "message": f"不支持的操作: {action}"
            })
        
        # 保存更新
        db[key] = license_info
        save_database(db)
        
        return jsonify({
            "success": True,
            "message": message
        })
        
    except Exception as e:
        logging.error(f"Error in modify_license: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"修改激活码错误: {str(e)}"
        })

# 获取激活码信息
@app.route('/license/info', methods=['GET'])
def get_license_info():
    try:
        key = request.args.get('key')
        
        if not key:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            })
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            })
        
        license_info = db[key]
        
        # 计算剩余天数
        try:
            expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
            remaining_days = (expire_date - datetime.now()).days
            license_info['remaining_days'] = remaining_days if remaining_days > 0 else 0
        except:
            license_info['remaining_days'] = 0
        
        return jsonify({
            "success": True,
            "info": license_info
        })
        
    except Exception as e:
        logging.error(f"Error in get_license_info: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取激活码信息错误: {str(e)}"
        })

# 下载程序文件
@app.route('/license/download', methods=['GET'])
def download_program():
    try:
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        program = request.args.get('program')
        
        if not key or not device_id or not program:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            }), 400
        
        # 检查请求的程序是否存在
        if program not in PROGRAM_PATHS:
            return jsonify({
                "success": False,
                "message": f"未知的程序: {program}"
            }), 404
            
        program_path = PROGRAM_PATHS[program]
        
        if not os.path.exists(program_path):
            return jsonify({
                "success": False,
                "message": f"程序文件不存在: {program}"
            }), 404
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            }), 404
        
        license_info = db[key]
        
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return jsonify({
                "success": False,
                "message": "激活码已过期"
            }), 403
        
        # 检查设备绑定
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return jsonify({
                "success": False,
                "message": "激活码已绑定到其他设备"
            }), 403
        
        # 检查权限级别
        permission_level = license_info.get('permission_level', 1)
        allowed_programs = PERMISSION_LEVELS.get(permission_level, [])
        
        if program not in allowed_programs:
            return jsonify({
                "success": False,
                "message": f"当前权限级别 ({permission_level}) 无法访问该程序"
            }), 403
        
        # 发送文件
        try:
            # 特定的文件名格式，以避免直接暴露文件路径
            program_name = program.replace(" ", "_") + ".py"
            
            response = send_file(
                program_path,
                as_attachment=True,
                download_name=program_name,
                mimetype='application/octet-stream'
            )
            
            # 添加不缓存的头信息
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
            
            # 记录下载
            logging.info(f"程序下载成功: {program}, 激活码: {key}, 设备: {device_id}")
            
            return response
            
        except Exception as e:
            logging.error(f"Error sending file: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"发送文件错误: {str(e)}"
            }), 500
        
    except Exception as e:
        logging.error(f"Error in download_program: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"下载程序错误: {str(e)}"
        }), 500

# 下载图标
@app.route('/license/download_icon', methods=['GET'])
def download_icon():
    try:
        if not os.path.exists(ICON_PATH):
            return jsonify({
                "success": False,
                "message": "图标文件不存在"
            }), 404
        
        # 发送文件
        try:
            response = send_file(
                ICON_PATH,
                as_attachment=True,
                download_name="icon.ico",
                mimetype='image/x-icon'
            )
            
            # 添加不缓存的头信息
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
            
            return response
            
        except Exception as e:
            logging.error(f"Error sending icon file: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"发送图标文件错误: {str(e)}"
            }), 500
        
    except Exception as e:
        logging.error(f"Error in download_icon: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"下载图标错误: {str(e)}"
        }), 500

# 下载欧陆扩展
@app.route('/extension/download', methods=['GET'])
def download_extension():
    try:
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        if not key or not device_id:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            }), 400
        
        # 检查扩展文件是否存在
        if not os.path.exists(EXTENSION_PATH):
            return jsonify({
                "success": False,
                "message": "扩展文件不存在"
            }), 404
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            }), 404
        
        license_info = db[key]
        
        # 检查是否过期
        expire_date = datetime.strptime(license_info['expire_date'], "%Y%m%d")
        if datetime.now() > expire_date:
            return jsonify({
                "success": False,
                "message": "激活码已过期"
            }), 403
        
        # 检查设备绑定
        if license_info.get('device_id') and license_info['device_id'] != device_id:
            return jsonify({
                "success": False,
                "message": "激活码已绑定到其他设备"
            }), 403
        
        # 发送文件
        try:
            # 从路径中提取文件名
            extension_filename = os.path.basename(EXTENSION_PATH)
            
            response = send_file(
                EXTENSION_PATH,
                as_attachment=True,
                download_name=extension_filename,
                mimetype='application/zip'
            )
            
            # 添加不缓存的头信息
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
            
            # 记录下载
            logging.info(f"欧陆扩展下载成功, 激活码: {key}, 设备: {device_id}")
            
            return response
            
        except Exception as e:
            logging.error(f"Error sending extension file: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"发送扩展文件错误: {str(e)}"
            }), 500
        
    except Exception as e:
        logging.error(f"Error in download_extension: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"下载扩展错误: {str(e)}"
        }), 500

# 获取激活码列表
@app.route('/license/list', methods=['GET'])
def list_licenses():
    try:
        # 加载数据库
        db = load_database()
        
        # 准备返回数据
        licenses = []
        
        for key, info in db.items():
            # 计算剩余天数
            try:
                expire_date = datetime.strptime(info['expire_date'], "%Y%m%d")
                remaining_days = (expire_date - datetime.now()).days
                info['remaining_days'] = remaining_days if remaining_days > 0 else 0
            except:
                info['remaining_days'] = 0
            
            # 复制必要信息
            license_data = {
                "key": key,
                "status": info.get('status', 'active'),
                "expire_date": info.get('expire_date', ''),
                "remaining_days": info.get('remaining_days', 0),
                "device_id": info.get('device_id', ''),
                "permission_level": info.get('permission_level', 1),
                "today_usage_count": info.get('today_usage_count', 0),
                "total_usage_count": info.get('total_usage_count', 0)
            }
            
            licenses.append(license_data)
        
        # 按剩余天数排序，过期的放在最后
        licenses.sort(key=lambda x: (x['remaining_days'] <= 0, -x['remaining_days']))
        
        return jsonify({
            "success": True,
            "licenses": licenses
        })
        
    except Exception as e:
        logging.error(f"Error in list_licenses: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取激活码列表错误: {str(e)}"
        })

# 删除激活码
@app.route('/license/delete', methods=['POST'])
def delete_license():
    try:
        data = request.json
        key = data.get('key')
        
        if not key:
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            })
        
        # 加载数据库
        db = load_database()
        
        # 检查激活码是否存在
        if key not in db:
            return jsonify({
                "success": False,
                "message": "激活码不存在"
            })
        
        # 删除激活码
        del db[key]
        save_database(db)
        
        return jsonify({
            "success": True,
            "message": "激活码已删除"
        })
        
    except Exception as e:
        logging.error(f"Error in delete_license: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"删除激活码错误: {str(e)}"
        })

if __name__ == "__main__":
    init_database()
    app.run(host="0.0.0.0", port=44285) 